/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --transition: all 0.2s ease-in-out;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* App Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: var(--surface-color);
    box-shadow: var(--shadow-lg);
    overflow: hidden; /* Prevent container overflow */
}

/* Header */
.app-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 1rem 2rem;
    box-shadow: var(--shadow-md);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.model-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.model-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.model-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: white;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
}

.model-select:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.model-select:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.model-select option {
    background: var(--primary-color);
    color: white;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: var(--success-color);
    animation: none;
}

.status-indicator.disconnected {
    background: var(--error-color);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
    min-height: 0; /* Allow flex item to shrink below content size */
    overflow: hidden; /* Prevent container overflow */
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.message {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease-in-out;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--primary-color);
    color: white;
}

.assistant-message .message-avatar {
    background: var(--secondary-color);
    color: white;
}

.message-content {
    flex: 1;
    background: var(--surface-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.user-message .message-content {
    background: var(--primary-color);
    color: white;
    margin-left: auto;
    max-width: 80%;
}

.assistant-message .message-content {
    max-width: 85%;
}

.system-message {
    justify-content: center;
    margin: 0.5rem 0;
}

.system-message .message-content {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-align: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    max-width: none;
}

.system-message .message-content i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* File Upload Section */
.file-upload-section {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 1rem;
}

.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.upload-area:hover,
.upload-area:focus {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    outline: none;
}

.upload-area.drag-over {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.upload-icon {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.upload-text {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.upload-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Upload Progress */
.upload-progress {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--background-color);
    border-radius: var(--border-radius);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Uploaded Files */
.uploaded-files {
    margin-top: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.uploaded-file {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 0.75rem;
    background: var(--background-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.uploaded-file:hover {
    background: var(--surface-color);
    box-shadow: var(--shadow-sm);
}

.uploaded-file[data-file-type="image"] .file-thumbnail {
    background: var(--success-color);
}

.uploaded-file[data-file-type="document"] .file-thumbnail {
    background: var(--error-color);
}

.uploaded-file[data-file-type="text"] .file-thumbnail {
    background: var(--primary-color);
}

.uploaded-file[data-file-type="code"] .file-thumbnail {
    background: var(--warning-color);
}

.file-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    object-fit: cover;
    background: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
    word-break: break-word;
    line-height: 1.3;
}

.file-details {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.file-preview {
    font-size: 0.75rem;
    color: var(--text-secondary);
    background: var(--surface-color);
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 100px;
    overflow-y: auto;
    line-height: 1.4;
}

.file-remove-btn {
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.75rem;
    transition: var(--transition);
    flex-shrink: 0;
}

.file-remove-btn:hover {
    background: #dc2626;
    transform: scale(1.1);
}

/* Message Files */
.message-files {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.user-message .message-files {
    border-top-color: rgba(255, 255, 255, 0.2);
}

.assistant-message .message-files {
    border-top-color: var(--border-color);
}

.message-file-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0;
    font-size: 0.875rem;
    opacity: 0.9;
}

.message-file-item i {
    width: 16px;
    text-align: center;
}

.message-file-item small {
    opacity: 0.7;
    margin-left: auto;
}

/* Content Sections */
.content-section {
    margin: 0.75rem 0;
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    font-weight: 500;
}

.section-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.copy-section-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.75rem;
    transition: var(--transition);
}

.copy-section-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

/* HTML Section */
.html-section .html-preview {
    padding: 1rem;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    margin: 0.5rem;
    border-radius: 4px;
    min-height: 100px;
}

.html-source {
    margin: 0.5rem;
}

.html-source summary {
    cursor: pointer;
    padding: 0.5rem;
    background: var(--background-color);
    border-radius: 4px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.html-source pre {
    margin-top: 0.5rem;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
}

/* Code Section */
.code-section pre {
    margin: 0;
    background: #f8f9fa !important;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Courier New', 'Fira Code', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
    border-radius: 0;
}

.code-section code {
    color: #333;
    font-family: inherit;
}

/* Override Prism styles for better integration */
.code-section pre[class*="language-"] {
    background: #f8f9fa !important;
    border: none;
    border-radius: 0;
    margin: 0;
}

.code-section code[class*="language-"] {
    background: transparent;
}

/* JSON Section */
.json-section pre {
    margin: 0;
    background: #f8f9fa;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
}

/* Markdown Section */
.markdown-section .markdown-content {
    padding: 1rem;
    background: var(--surface-color);
}

.markdown-content strong {
    font-weight: 600;
    color: var(--text-primary);
}

.markdown-content em {
    font-style: italic;
    color: var(--text-secondary);
}

.markdown-content code {
    background: var(--background-color);
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* Text Section */
.text-section {
    border: none;
    margin: 0.5rem 0;
}

.text-section p {
    margin: 0;
    line-height: 1.6;
}

/* Message Copy Button */
.message-copy-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-secondary);
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.75rem;
    opacity: 0;
    transition: var(--transition);
}

.message-content {
    position: relative;
}

.message-content:hover .message-copy-btn {
    opacity: 1;
}

.message-copy-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.user-message .message-copy-btn {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
}

.user-message .message-copy-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* Input Area */
.input-area {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 1rem;
}

.input-container {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
}

.message-input {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-family: inherit;
    font-size: 1rem;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    transition: var(--transition);
}

.message-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.send-button {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    min-width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-button:hover:not(:disabled) {
    background: var(--primary-hover);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-button.has-files {
    background: var(--success-color);
    position: relative;
}

.send-button.has-files:hover:not(:disabled) {
    background: #059669;
}

.send-button.has-files::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: var(--warning-color);
    border-radius: 50%;
    border: 2px solid var(--surface-color);
}

/* Notifications */
.notifications {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    max-width: 400px;
}

.notification {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--error-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .model-selector {
        order: 1;
    }

    .connection-status {
        order: 2;
    }

    .chat-container {
        padding: 0.5rem;
    }

    .upload-area {
        padding: 1.5rem 1rem;
    }

    .message-content {
        max-width: 95%;
    }

    .user-message .message-content {
        max-width: 90%;
    }

    .model-select {
        font-size: 0.8rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-spinner {
    background: var(--surface-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1500;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--background-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-body h3 {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.modal-body h3:first-child {
    margin-top: 0;
}

.modal-body ul {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.modal-body li {
    margin-bottom: 0.25rem;
}

.modal-body kbd {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.2rem 0.4rem;
    font-family: monospace;
    font-size: 0.875rem;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
}
