/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --transition: all 0.2s ease-in-out;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', <PERSON><PERSON>, '<PERSON><PERSON>', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* App Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: var(--surface-color);
    box-shadow: var(--shadow-lg);
    overflow: hidden; /* Prevent container overflow */
}

/* Header */
.app-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 1rem 2rem;
    box-shadow: var(--shadow-md);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.model-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.model-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.model-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: white;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
}

.model-select:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.model-select:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.model-select option {
    background: var(--primary-color);
    color: white;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: var(--success-color);
    animation: none;
}

.status-indicator.disconnected {
    background: var(--error-color);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
    min-height: 0; /* Allow flex item to shrink below content size */
    overflow: hidden; /* Prevent container overflow */
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.message {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease-in-out;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: var(--primary-color);
    color: white;
}

.assistant-message .message-avatar {
    background: var(--secondary-color);
    color: white;
}

.message-content {
    flex: 1;
    background: var(--surface-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.user-message .message-content {
    background: var(--primary-color);
    color: white;
    margin-left: auto;
    max-width: 80%;
}

.assistant-message .message-content {
    max-width: 85%;
}

.system-message {
    justify-content: center;
    margin: 0.5rem 0;
}

.system-message .message-content {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-align: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    max-width: none;
}

.system-message .message-content i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Compact File Upload */
.file-input-hidden {
    display: none;
}

.upload-info-tooltip {
    padding: 0.5rem;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.75rem;
    opacity: 0;
    transition: var(--transition);
}

.upload-info-tooltip.show {
    opacity: 1;
}

/* Integrated Upload Progress */
.upload-progress-integrated {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.progress-bar-integrated {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.progress-fill-integrated {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
    border-radius: 2px;
}

.progress-text-integrated {
    font-size: 0.75rem;
    color: var(--text-secondary);
    display: block;
    text-align: center;
}

/* Compact Uploaded Files */
.uploaded-files-compact {
    margin-bottom: 0.5rem;
    max-height: 120px;
    overflow-y: auto;
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 0.5rem;
}

.uploaded-file {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--surface-color);
    border-radius: 4px;
    margin-bottom: 0.25rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
    font-size: 0.875rem;
}

.uploaded-file:hover {
    background: var(--surface-color);
    box-shadow: var(--shadow-sm);
}

.uploaded-file[data-file-type="image"] .file-thumbnail {
    background: var(--success-color);
}

.uploaded-file[data-file-type="document"] .file-thumbnail {
    background: var(--error-color);
}

.uploaded-file[data-file-type="text"] .file-thumbnail {
    background: var(--primary-color);
}

.uploaded-file[data-file-type="code"] .file-thumbnail {
    background: var(--warning-color);
}

.file-thumbnail {
    width: 24px;
    height: 24px;
    border-radius: 3px;
    object-fit: cover;
    background: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    font-size: 0.75rem;
}

.file-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 3px;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 500;
    word-break: break-word;
    line-height: 1.2;
    flex: 1;
    min-width: 0;
}

.file-details {
    font-size: 0.7rem;
    color: var(--text-secondary);
    opacity: 0.8;
}

.file-preview {
    display: none; /* Hidden in compact view */
}

.file-remove-btn {
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.6rem;
    transition: var(--transition);
    flex-shrink: 0;
    opacity: 0.7;
}

.file-remove-btn:hover {
    background: #dc2626;
    opacity: 1;
    transform: scale(1.1);
}

/* Message Files */
.message-files {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.user-message .message-files {
    border-top-color: rgba(255, 255, 255, 0.2);
}

.assistant-message .message-files {
    border-top-color: var(--border-color);
}

.message-file-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0;
    font-size: 0.875rem;
    opacity: 0.9;
}

.message-file-item i {
    width: 16px;
    text-align: center;
}

.message-file-item small {
    opacity: 0.7;
    margin-left: auto;
}

/* Content Sections */
.content-section {
    margin: 0.75rem 0;
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    font-weight: 500;
}

.section-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.copy-section-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.75rem;
    transition: var(--transition);
}

.copy-section-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

/* HTML Section */
.html-section .html-preview {
    padding: 1rem;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    margin: 0.5rem;
    border-radius: 4px;
    min-height: 100px;
}

.html-source {
    margin: 0.5rem;
}

.html-source summary {
    cursor: pointer;
    padding: 0.5rem;
    background: var(--background-color);
    border-radius: 4px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.html-source pre {
    margin-top: 0.5rem;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
}

/* Code Section */
.code-section pre {
    margin: 0;
    background: #f8f9fa !important;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Courier New', 'Fira Code', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
    border-radius: 0;
}

.code-section code {
    color: #333;
    font-family: inherit;
}

/* Override Prism styles for better integration */
.code-section pre[class*="language-"] {
    background: #f8f9fa !important;
    border: none;
    border-radius: 0;
    margin: 0;
}

.code-section code[class*="language-"] {
    background: transparent;
}

/* JSON Section */
.json-section pre {
    margin: 0;
    background: #f8f9fa;
    padding: 1rem;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
}

/* Markdown Section */
.markdown-section .markdown-content {
    padding: 1rem;
    background: var(--surface-color);
}

.markdown-content strong {
    font-weight: 600;
    color: var(--text-primary);
}

.markdown-content em {
    font-style: italic;
    color: var(--text-secondary);
}

.markdown-content code {
    background: var(--background-color);
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* Japanese Section */
.japanese-section {
    border-left: 4px solid #ff6b6b;
}

.japanese-section .section-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.japanese-section .section-label {
    color: white;
}

.encoding-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.encoding-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    padding: 0.2rem 0.4rem;
    cursor: pointer;
    font-size: 0.7rem;
    transition: var(--transition);
}

.encoding-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.japanese-content {
    padding: 1rem;
    background: var(--surface-color);
    font-family: 'Noto Sans JP', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', sans-serif;
    line-height: 1.8;
    font-size: 1rem;
    color: var(--text-primary);
    font-weight: 400;
}

/* Ensure proper Japanese text rendering */
.japanese-content {
    word-break: keep-all;
    overflow-wrap: break-word;
    text-align: left;
    writing-mode: horizontal-tb;
}

/* Support for vertical Japanese text if needed */
.japanese-content.vertical {
    writing-mode: vertical-rl;
    text-orientation: mixed;
}

/* Text Section */
.text-section {
    border: none;
    margin: 0.5rem 0;
}

.text-section p {
    margin: 0;
    line-height: 1.6;
}

/* Message Copy Button */
.message-copy-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.1);
    color: var(--text-secondary);
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.75rem;
    opacity: 0;
    transition: var(--transition);
}

.message-content {
    position: relative;
}

.message-content:hover .message-copy-btn {
    opacity: 1;
}

.message-copy-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.user-message .message-copy-btn {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
}

.user-message .message-copy-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* Input Area */
.input-area {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 1rem;
}

.input-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-wrapper {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    transition: var(--transition);
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-wrapper.drag-over {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.attach-button {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
}

.attach-button:hover {
    background: var(--background-color);
    color: var(--primary-color);
}

.attach-button.has-files {
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.message-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 0.5rem;
    font-family: inherit;
    font-size: 1rem;
    resize: none;
    min-height: 36px;
    max-height: 120px;
    transition: var(--transition);
}

.message-input:focus {
    outline: none;
}

.send-button {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-button:hover:not(:disabled) {
    background: var(--primary-hover);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-button.has-files {
    background: var(--success-color);
    position: relative;
}

.send-button.has-files:hover:not(:disabled) {
    background: #059669;
}

.send-button.has-files::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: var(--warning-color);
    border-radius: 50%;
    border: 2px solid var(--surface-color);
}

/* Notifications */
.notifications {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    max-width: 400px;
}

.notification {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--error-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .model-selector {
        order: 1;
    }

    .connection-status {
        order: 2;
    }

    .chat-container {
        padding: 0.5rem;
    }

    .input-wrapper {
        padding: 0.4rem;
        gap: 0.4rem;
    }

    .attach-button,
    .send-button {
        min-width: 32px;
        height: 32px;
        padding: 0.4rem;
    }

    .message-input {
        min-height: 32px;
        padding: 0.4rem;
    }

    .message-content {
        max-width: 95%;
    }

    .user-message .message-content {
        max-width: 90%;
    }

    .model-select {
        font-size: 0.8rem;
        padding: 0.2rem 0.4rem;
    }
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-spinner {
    background: var(--surface-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1500;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--background-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-body h3 {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.modal-body h3:first-child {
    margin-top: 0;
}

.modal-body ul {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.modal-body li {
    margin-bottom: 0.25rem;
}

.modal-body kbd {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.2rem 0.4rem;
    font-family: monospace;
    font-size: 0.875rem;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
}
