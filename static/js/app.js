// DeepSeek Chat Application JavaScript

class ChatApp {
    constructor() {
        this.socket = null;
        this.conversationHistory = [];
        this.isConnected = false;
        this.currentResponse = '';
        this.uploadedFiles = [];
        this.selectedModel = 'deepseek-chat'; // Default model
        this.debugMode = localStorage.getItem('debugMode') === 'true';
        this.logs = [];

        this.initializeElements();
        this.initializeSocket();
        this.setupEventListeners();
        this.setupFileUpload();
        this.setupDebugMode();
    }

    log(level, message, data = null) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            data
        };

        this.logs.push(logEntry);

        // Keep only last 1000 logs
        if (this.logs.length > 1000) {
            this.logs = this.logs.slice(-1000);
        }

        if (this.debugMode) {
            console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`, data || '');
        }

        // Store in localStorage for persistence
        localStorage.setItem('chatAppLogs', JSON.stringify(this.logs.slice(-100)));
    }

    initializeElements() {
        this.elements = {
            chatMessages: document.getElementById('chatMessages'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendButton'),
            connectionStatus: document.getElementById('connectionStatus'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            attachButton: document.getElementById('attachButton'),
            fileInput: document.getElementById('fileInput'),
            uploadProgress: document.getElementById('uploadProgress'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            uploadedFiles: document.getElementById('uploadedFiles'),
            notifications: document.getElementById('notifications'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            modelSelect: document.getElementById('modelSelect'),
            uploadInfo: document.getElementById('uploadInfo')
        };
    }

    initializeSocket() {
        this.log('info', 'Initializing socket connection');
        this.socket = io();

        this.socket.on('connect', () => {
            this.isConnected = true;
            this.log('info', 'Socket connected', { socketId: this.socket.id });
            this.updateConnectionStatus('Connected', 'connected');
            this.showNotification('Connected to DeepSeek Chat', 'success');
        });

        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.log('warning', 'Socket disconnected');
            this.updateConnectionStatus('Disconnected', 'disconnected');
            this.showNotification('Connection lost. Trying to reconnect...', 'error');
        });

        this.socket.on('status', (data) => {
            this.log('info', 'Status message received', data);
            this.showNotification(data.message, 'info');
        });

        this.socket.on('message_chunk', (data) => {
            this.log('debug', 'Message chunk received', {
                contentLength: data.content ? data.content.length : 0,
                isComplete: data.is_complete
            });
            this.handleMessageChunk(data);
        });

        this.socket.on('error', (data) => {
            this.log('error', 'Socket error received', data);
            this.showNotification(data.message, 'error');
            this.enableInput();
        });
    }

    setupDebugMode() {
        // Add debug toggle to header
        const debugToggle = document.createElement('button');
        debugToggle.innerHTML = this.debugMode ? '🐛 Debug: ON' : '🐛 Debug: OFF';
        debugToggle.className = 'debug-toggle';
        debugToggle.onclick = () => this.toggleDebugMode();

        const headerControls = document.querySelector('.header-controls');
        if (headerControls) {
            headerControls.appendChild(debugToggle);
        }

        // Add keyboard shortcut for debug mode
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.toggleDebugMode();
            }
        });
    }

    toggleDebugMode() {
        this.debugMode = !this.debugMode;
        localStorage.setItem('debugMode', this.debugMode.toString());

        const debugToggle = document.querySelector('.debug-toggle');
        if (debugToggle) {
            debugToggle.innerHTML = this.debugMode ? '🐛 Debug: ON' : '🐛 Debug: OFF';
        }

        this.log('info', `Debug mode ${this.debugMode ? 'enabled' : 'disabled'}`);

        if (this.debugMode) {
            this.showDebugPanel();
        } else {
            this.hideDebugPanel();
        }
    }

    setupEventListeners() {
        // Send button click
        this.elements.sendButton.addEventListener('click', () => this.sendMessage());
        
        // Input auto-resize and enter key handling
        this.elements.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
            this.updateSendButtonState();
        });
        this.elements.messageInput.addEventListener('keydown', (e) => this.handleInputKeydown(e));
        
        // Escape key to clear input
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.elements.messageInput.value = '';
                this.autoResizeTextarea();
            }
        });

        // Model selection change
        this.elements.modelSelect.addEventListener('change', (e) => {
            this.selectedModel = e.target.value;
            const modelName = e.target.options[e.target.selectedIndex].text;
            this.showNotification(`Switched to ${modelName}`, 'info');
            this.addModelChangeMessage(modelName);
        });
    }

    setupFileUpload() {
        const attachButton = this.elements.attachButton;
        const fileInput = this.elements.fileInput;
        const inputWrapper = document.querySelector('.input-wrapper');

        // Attach button click
        attachButton.addEventListener('click', () => {
            fileInput.click();
            this.showUploadInfo();
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleMultipleFileUpload(Array.from(e.target.files));
            }
        });

        // Drag and drop on input area
        inputWrapper.addEventListener('dragover', (e) => {
            e.preventDefault();
            inputWrapper.classList.add('drag-over');
        });

        inputWrapper.addEventListener('dragleave', (e) => {
            e.preventDefault();
            inputWrapper.classList.remove('drag-over');
        });

        inputWrapper.addEventListener('drop', (e) => {
            e.preventDefault();
            inputWrapper.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleMultipleFileUpload(Array.from(files));
            }
        });

        // Hide upload info when clicking elsewhere
        document.addEventListener('click', (e) => {
            if (!attachButton.contains(e.target)) {
                this.hideUploadInfo();
            }
        });
    }

    updateConnectionStatus(text, status) {
        this.elements.statusText.textContent = text;
        this.elements.statusIndicator.className = `status-indicator ${status}`;
    }

    autoResizeTextarea() {
        const textarea = this.elements.messageInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    handleInputKeydown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    }

    sendMessage() {
        const message = this.elements.messageInput.value.trim();
        if (!message || !this.isConnected) {
            this.log('warning', 'Cannot send message', {
                hasMessage: !!message,
                isConnected: this.isConnected
            });
            return;
        }

        // Get currently uploaded files to include with message
        const attachedFiles = this.uploadedFiles.map(file => file.id);

        this.log('info', 'Sending message', {
            messageLength: message.length,
            attachedFiles: attachedFiles.length,
            model: this.selectedModel,
            historyLength: this.conversationHistory.length
        });

        // Add user message to chat (show files in UI)
        this.addMessage(message, 'user', attachedFiles);

        // Add to conversation history (without file content for history)
        this.conversationHistory.push({ role: 'user', content: message });

        // Clear input and disable
        this.elements.messageInput.value = '';
        this.autoResizeTextarea();
        this.disableInput();

        // Send to server with attached files and selected model
        const messageData = {
            message: message,
            history: this.conversationHistory,
            files: attachedFiles,
            model: this.selectedModel
        };

        this.log('debug', 'Emitting send_message', messageData);
        this.socket.emit('send_message', messageData);

        // Clear uploaded files after sending (they're now part of the conversation)
        this.clearUploadedFiles();

        // Start assistant message
        this.startAssistantMessage();
    }

    addMessage(content, sender, attachedFiles = []) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        // Add message text
        const textContent = document.createElement('p');
        textContent.innerHTML = this.escapeHtml(content);
        messageContent.appendChild(textContent);

        // Add attached files display if any
        if (attachedFiles && attachedFiles.length > 0) {
            const filesDiv = document.createElement('div');
            filesDiv.className = 'message-files';

            attachedFiles.forEach(fileId => {
                const fileInfo = this.uploadedFiles.find(f => f.id === fileId);
                if (fileInfo) {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'message-file-item';
                    fileItem.innerHTML = `
                        <i class="fas ${this.getFileIcon(fileInfo.type, fileInfo.filename)}"></i>
                        <span>${this.escapeHtml(fileInfo.filename)}</span>
                        <small>(${this.formatFileSize(fileInfo.size)})</small>
                    `;
                    filesDiv.appendChild(fileItem);
                }
            });

            if (filesDiv.children.length > 0) {
                messageContent.appendChild(filesDiv);
            }
        }

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);

        this.elements.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();

        return messageContent;
    }

    startAssistantMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant-message';

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = '<i class="fas fa-robot"></i>';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = '<p></p>';

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);

        this.elements.chatMessages.appendChild(messageDiv);
        this.currentMessageElement = messageContent.querySelector('p');
        this.currentResponse = '';

        this.log('info', 'Started assistant message');

        // Set up timeout to detect stalled streams
        this.streamTimeout = setTimeout(() => {
            this.log('error', 'Stream timeout - no response received');
            this.handleStreamTimeout();
        }, 30000); // 30 second timeout

        // Set up chunk timeout to detect stalled mid-stream
        this.lastChunkTime = Date.now();
        this.chunkTimeout = setInterval(() => {
            const timeSinceLastChunk = Date.now() - this.lastChunkTime;
            if (timeSinceLastChunk > 10000) { // 10 seconds without chunks
                this.log('warning', 'No chunks received for 10 seconds', { timeSinceLastChunk });
                if (timeSinceLastChunk > 20000) { // 20 seconds - force completion
                    this.log('error', 'Forcing stream completion due to timeout');
                    this.handleStreamTimeout();
                }
            }
        }, 5000);

        this.scrollToBottom();
    }

    handleMessageChunk(data) {
        // Update last chunk time
        this.lastChunkTime = Date.now();

        if (data.is_complete) {
            // Response complete
            this.log('info', 'Message stream completed', {
                totalLength: data.full_response ? data.full_response.length : 0,
                currentResponseLength: this.currentResponse.length
            });

            // Clear timeouts
            this.clearStreamTimeouts();

            const finalResponse = data.full_response || this.currentResponse;
            this.conversationHistory.push({ role: 'assistant', content: finalResponse });
            this.finalizeAssistantMessage(finalResponse);
            this.enableInput();
            this.elements.messageInput.focus();

            // Reset current response
            this.currentResponse = '';
        } else {
            // Add chunk to current response
            if (data.content) {
                this.currentResponse += data.content;
                if (this.currentMessageElement) {
                    this.currentMessageElement.textContent = this.currentResponse;
                    this.scrollToBottom();
                }
            } else {
                this.log('warning', 'Received empty content chunk', data);
            }
        }
    }

    clearStreamTimeouts() {
        if (this.streamTimeout) {
            clearTimeout(this.streamTimeout);
            this.streamTimeout = null;
        }
        if (this.chunkTimeout) {
            clearInterval(this.chunkTimeout);
            this.chunkTimeout = null;
        }
    }

    handleStreamTimeout() {
        this.log('error', 'Stream timeout occurred', {
            currentResponseLength: this.currentResponse.length,
            lastChunkTime: this.lastChunkTime
        });

        this.clearStreamTimeouts();

        // Force completion with current response
        if (this.currentResponse) {
            this.conversationHistory.push({ role: 'assistant', content: this.currentResponse });
            this.finalizeAssistantMessage(this.currentResponse);
        } else {
            // Add error message
            const errorMessage = "⚠️ Response timed out. Please try again.";
            this.conversationHistory.push({ role: 'assistant', content: errorMessage });
            if (this.currentMessageElement) {
                this.currentMessageElement.textContent = errorMessage;
            }
        }

        this.enableInput();
        this.elements.messageInput.focus();
        this.currentResponse = '';

        this.showNotification('Response timed out. Please try again.', 'error');
    }

    async handleMultipleFileUpload(files) {
        // Validate all files first
        const validFiles = [];
        const invalidFiles = [];

        for (const file of files) {
            if (this.validateFile(file)) {
                validFiles.push(file);
            } else {
                invalidFiles.push(file.name);
            }
        }

        if (invalidFiles.length > 0) {
            this.showNotification(`Invalid files: ${invalidFiles.join(', ')}`, 'warning');
        }

        if (validFiles.length === 0) {
            return;
        }

        // Show progress
        this.showUploadProgress(`Uploading ${validFiles.length} file(s)...`);

        const formData = new FormData();
        validFiles.forEach(file => {
            formData.append('files', file);
        });

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // Add all uploaded files
                result.files.forEach(fileInfo => {
                    this.addUploadedFile(fileInfo);
                });

                this.showNotification(result.message, 'success');

                // Show warnings if any
                if (result.warnings && result.warnings.length > 0) {
                    setTimeout(() => {
                        this.showNotification(`Some files failed: ${result.warnings.join('; ')}`, 'warning');
                    }, 1000);
                }
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            this.showNotification('Upload failed: ' + error.message, 'error');
        } finally {
            this.hideUploadProgress();
        }
    }

    // Keep the old method for backward compatibility
    async handleFileUpload(file) {
        return this.handleMultipleFileUpload([file]);
    }

    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            // Images
            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/svg+xml',
            // Documents
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/vnd.ms-powerpoint',
            'application/rtf',
            // Text and code files
            'text/plain', 'text/markdown', 'text/html', 'text/css', 'text/javascript',
            'application/json', 'application/xml', 'text/csv',
            'text/x-python', 'text/x-java-source', 'text/x-c++src', 'text/x-csrc',
            'text/x-chdr', 'text/x-php', 'text/x-ruby', 'text/x-go', 'text/x-rust',
            'text/x-sql', 'text/x-shellscript', 'text/yaml'
        ];

        if (file.size > maxSize) {
            this.showNotification(`File "${file.name}" exceeds 10MB limit`, 'error');
            return false;
        }

        // Check file extension as fallback for MIME type detection
        const fileName = file.name.toLowerCase();
        const allowedExtensions = [
            '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg',
            '.pdf', '.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt', '.rtf',
            '.txt', '.md', '.html', '.htm', '.css', '.js', '.json', '.xml', '.csv', '.log',
            '.py', '.java', '.cpp', '.c', '.h', '.php', '.rb', '.go', '.rs', '.sql', '.sh', '.yaml', '.yml'
        ];

        const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
        const hasValidMimeType = allowedTypes.includes(file.type) || file.type.startsWith('text/');

        if (!hasValidExtension && !hasValidMimeType) {
            this.showNotification(`File type not supported: "${file.name}"`, 'error');
            return false;
        }

        return true;
    }

    showUploadProgress(message = 'Uploading...') {
        this.elements.uploadProgress.style.display = 'block';
        this.elements.progressFill.style.width = '0%';
        this.elements.progressText.textContent = message;

        // Simulate progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 30;
            if (progress >= 90) {
                clearInterval(interval);
                progress = 90;
            }
            this.elements.progressFill.style.width = progress + '%';
        }, 100);

        this.uploadInterval = interval;
    }

    hideUploadProgress() {
        if (this.uploadInterval) {
            clearInterval(this.uploadInterval);
        }
        this.elements.progressFill.style.width = '100%';
        this.elements.progressText.textContent = 'Complete!';
        
        setTimeout(() => {
            this.elements.uploadProgress.style.display = 'none';
        }, 1000);
    }

    addUploadedFile(fileInfo) {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'uploaded-file';
        fileDiv.setAttribute('data-file-type', fileInfo.type || 'other');
        fileDiv.setAttribute('data-file-id', fileInfo.id);

        const thumbnail = document.createElement('div');
        thumbnail.className = 'file-thumbnail';

        if (fileInfo.thumbnail) {
            const img = document.createElement('img');
            img.src = `/uploads/${fileInfo.thumbnail}`;
            img.alt = fileInfo.filename;
            thumbnail.appendChild(img);
        } else {
            // Show appropriate icon based on file type
            const icon = this.getFileIcon(fileInfo.type, fileInfo.filename);
            thumbnail.innerHTML = `<i class="fas ${icon}"></i>`;
        }

        const fileInfoDiv = document.createElement('div');
        fileInfoDiv.className = 'file-info';

        let previewHtml = '';
        if (fileInfo.preview && (fileInfo.type === 'text' || fileInfo.type === 'code')) {
            previewHtml = `<div class="file-preview">${this.escapeHtml(fileInfo.preview)}</div>`;
        }

        // Compact display - just filename and size
        fileInfoDiv.innerHTML = `
            <div class="file-name" title="${this.escapeHtml(fileInfo.filename)}">${this.escapeHtml(fileInfo.filename)}</div>
            <div class="file-details">${this.formatFileSize(fileInfo.size)}</div>
        `;

        // Add remove button
        const removeButton = document.createElement('button');
        removeButton.className = 'file-remove-btn';
        removeButton.innerHTML = '<i class="fas fa-times"></i>';
        removeButton.title = 'Remove file';
        removeButton.onclick = () => this.removeUploadedFile(fileInfo.id);

        fileDiv.appendChild(thumbnail);
        fileDiv.appendChild(fileInfoDiv);
        fileDiv.appendChild(removeButton);

        this.elements.uploadedFiles.appendChild(fileDiv);
        this.uploadedFiles.push(fileInfo);

        // Show indication that files are ready to be sent
        this.updateSendButtonState();
    }

    removeUploadedFile(fileId) {
        // Remove from array
        this.uploadedFiles = this.uploadedFiles.filter(file => file.id !== fileId);

        // Remove from DOM
        const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
        if (fileElement) {
            fileElement.remove();
        }

        this.updateSendButtonState();
    }

    clearUploadedFiles() {
        this.uploadedFiles = [];
        this.elements.uploadedFiles.innerHTML = '';
        this.updateSendButtonState();
    }

    updateSendButtonState() {
        const hasFiles = this.uploadedFiles.length > 0;
        const hasMessage = this.elements.messageInput.value.trim().length > 0;

        if (hasFiles && !hasMessage) {
            this.elements.messageInput.placeholder = 'Ask me about the uploaded files...';
        } else {
            this.elements.messageInput.placeholder = 'Type your message here...';
        }

        // Update attach button appearance if files are attached
        if (hasFiles) {
            this.elements.attachButton.classList.add('has-files');
            this.elements.sendButton.classList.add('has-files');
            this.elements.uploadedFiles.style.display = 'block';
        } else {
            this.elements.attachButton.classList.remove('has-files');
            this.elements.sendButton.classList.remove('has-files');
            this.elements.uploadedFiles.style.display = 'none';
        }
    }

    showUploadInfo() {
        this.elements.uploadInfo.classList.add('show');
    }

    hideUploadInfo() {
        this.elements.uploadInfo.classList.remove('show');
    }

    addModelChangeMessage(modelName) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message system-message';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = `<p><i class="fas fa-cog"></i> Switched to <strong>${this.escapeHtml(modelName)}</strong></p>`;

        messageDiv.appendChild(messageContent);
        this.elements.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    finalizeAssistantMessage(fullResponse) {
        if (!this.currentMessageElement) return;

        // Parse and format the response
        const formattedContent = this.formatResponseContent(fullResponse);

        // Replace the temporary text content with formatted content
        const messageContent = this.currentMessageElement.parentElement;
        messageContent.innerHTML = formattedContent;

        // Add copy button
        this.addCopyButton(messageContent, fullResponse);

        // Apply syntax highlighting if Prism is available
        if (typeof Prism !== 'undefined') {
            Prism.highlightAllUnder(messageContent);
        }

        this.scrollToBottom();
    }

    formatResponseContent(content) {
        // Detect different content types and format accordingly
        const sections = this.parseContentSections(content);
        let formattedHtml = '';

        sections.forEach(section => {
            switch (section.type) {
                case 'html':
                    formattedHtml += this.formatHtmlSection(section.content);
                    break;
                case 'code':
                    formattedHtml += this.formatCodeSection(section.content, section.language);
                    break;
                case 'json':
                    formattedHtml += this.formatJsonSection(section.content);
                    break;
                case 'markdown':
                    formattedHtml += this.formatMarkdownSection(section.content);
                    break;
                case 'japanese':
                    formattedHtml += this.formatJapaneseSection(section.content);
                    break;
                default:
                    formattedHtml += this.formatTextSection(section.content);
            }
        });

        return formattedHtml;
    }

    parseContentSections(content) {
        const sections = [];
        const lines = content.split('\n');
        let currentSection = { type: 'text', content: '' };
        let inCodeBlock = false;
        let codeLanguage = '';

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // Detect code blocks
            if (line.startsWith('```')) {
                if (inCodeBlock) {
                    // End of code block
                    sections.push({
                        type: 'code',
                        content: currentSection.content.trim(),
                        language: codeLanguage
                    });
                    currentSection = { type: 'text', content: '' };
                    inCodeBlock = false;
                    codeLanguage = '';
                } else {
                    // Start of code block
                    if (currentSection.content.trim()) {
                        sections.push(currentSection);
                    }
                    codeLanguage = line.substring(3).trim();
                    currentSection = { type: 'code', content: '' };
                    inCodeBlock = true;
                }
                continue;
            }

            if (inCodeBlock) {
                currentSection.content += line + '\n';
            } else {
                // Detect HTML content
                if (line.trim().startsWith('<') && line.trim().endsWith('>')) {
                    if (currentSection.type !== 'html') {
                        if (currentSection.content.trim()) {
                            sections.push(currentSection);
                        }
                        currentSection = { type: 'html', content: '' };
                    }
                }
                // Detect JSON content
                else if ((line.trim().startsWith('{') || line.trim().startsWith('[')) && currentSection.type !== 'json') {
                    if (currentSection.content.trim()) {
                        sections.push(currentSection);
                    }
                    currentSection = { type: 'json', content: '' };
                }
                // Detect Japanese content (Shift_JIS indicators)
                else if (this.containsJapanese(line)) {
                    if (currentSection.type !== 'japanese') {
                        if (currentSection.content.trim()) {
                            sections.push(currentSection);
                        }
                        currentSection = { type: 'japanese', content: '' };
                    }
                }

                currentSection.content += line + '\n';
            }
        }

        if (currentSection.content.trim()) {
            sections.push(currentSection);
        }

        return sections;
    }

    containsJapanese(text) {
        // Check for Japanese characters (Hiragana, Katakana, Kanji)
        const japaneseRegex = /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\u3400-\u4DBF]/;
        return japaneseRegex.test(text);
    }

    formatHtmlSection(content) {
        const sectionId = 'html-' + Math.random().toString(36).substr(2, 9);
        return `
            <div class="content-section html-section">
                <div class="section-header">
                    <span class="section-label"><i class="fab fa-html5"></i> HTML Content</span>
                    <button class="copy-section-btn" onclick="window.chatApp.copyToClipboard('${sectionId}', 'html')" title="Copy HTML">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <div class="html-preview" id="${sectionId}">
                    ${content}
                </div>
                <details class="html-source">
                    <summary>View HTML Source</summary>
                    <pre><code class="language-html">${this.escapeHtml(content)}</code></pre>
                </details>
            </div>
        `;
    }

    formatCodeSection(content, language) {
        const sectionId = 'code-' + Math.random().toString(36).substr(2, 9);
        const languageIcon = this.getLanguageIcon(language);
        return `
            <div class="content-section code-section">
                <div class="section-header">
                    <span class="section-label"><i class="${languageIcon}"></i> ${language.toUpperCase() || 'Code'}</span>
                    <button class="copy-section-btn" onclick="window.chatApp.copyToClipboard('${sectionId}', 'code')" title="Copy Code">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <pre id="${sectionId}"><code class="language-${language}">${this.escapeHtml(content)}</code></pre>
            </div>
        `;
    }

    formatJsonSection(content) {
        const sectionId = 'json-' + Math.random().toString(36).substr(2, 9);
        let formattedJson = content;
        try {
            const parsed = JSON.parse(content);
            formattedJson = JSON.stringify(parsed, null, 2);
        } catch (e) {
            // Keep original if parsing fails
        }

        return `
            <div class="content-section json-section">
                <div class="section-header">
                    <span class="section-label"><i class="fas fa-code"></i> JSON</span>
                    <button class="copy-section-btn" onclick="window.chatApp.copyToClipboard('${sectionId}', 'json')" title="Copy JSON">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <pre id="${sectionId}"><code class="language-json">${this.escapeHtml(formattedJson)}</code></pre>
            </div>
        `;
    }

    formatMarkdownSection(content) {
        const sectionId = 'md-' + Math.random().toString(36).substr(2, 9);
        return `
            <div class="content-section markdown-section">
                <div class="section-header">
                    <span class="section-label"><i class="fab fa-markdown"></i> Markdown</span>
                    <button class="copy-section-btn" onclick="window.chatApp.copyToClipboard('${sectionId}', 'markdown')" title="Copy Markdown">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <div class="markdown-content" id="${sectionId}">${this.parseSimpleMarkdown(content)}</div>
            </div>
        `;
    }

    formatJapaneseSection(content) {
        const sectionId = 'japanese-' + Math.random().toString(36).substr(2, 9);
        return `
            <div class="content-section japanese-section">
                <div class="section-header">
                    <span class="section-label"><i class="fas fa-language"></i> Japanese Text</span>
                    <div class="encoding-buttons">
                        <button class="encoding-btn" onclick="window.chatApp.copyAsEncoding('${sectionId}', 'utf8')" title="Copy as UTF-8">
                            UTF-8
                        </button>
                        <button class="encoding-btn" onclick="window.chatApp.copyAsEncoding('${sectionId}', 'shiftjis')" title="Copy as Shift_JIS">
                            Shift_JIS
                        </button>
                        <button class="copy-section-btn" onclick="window.chatApp.copyToClipboard('${sectionId}', 'japanese')" title="Copy Japanese Text">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="japanese-content" id="${sectionId}" lang="ja">${this.escapeHtml(content).replace(/\n/g, '<br>')}</div>
            </div>
        `;
    }

    formatTextSection(content) {
        // Check if content contains Japanese characters
        if (this.containsJapanese(content)) {
            return this.formatJapaneseSection(content);
        }
        return `<div class="content-section text-section"><p>${this.escapeHtml(content).replace(/\n/g, '<br>')}</p></div>`;
    }

    getLanguageIcon(language) {
        const icons = {
            'javascript': 'fab fa-js-square',
            'js': 'fab fa-js-square',
            'python': 'fab fa-python',
            'py': 'fab fa-python',
            'html': 'fab fa-html5',
            'css': 'fab fa-css3-alt',
            'java': 'fab fa-java',
            'php': 'fab fa-php',
            'react': 'fab fa-react',
            'vue': 'fab fa-vuejs',
            'angular': 'fab fa-angular',
            'node': 'fab fa-node-js'
        };
        return icons[language.toLowerCase()] || 'fas fa-code';
    }

    parseSimpleMarkdown(content) {
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    addCopyButton(messageContent, fullResponse) {
        const copyButton = document.createElement('button');
        copyButton.className = 'message-copy-btn';
        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
        copyButton.title = 'Copy entire response';
        copyButton.onclick = () => this.copyToClipboard(null, 'full', fullResponse);

        messageContent.appendChild(copyButton);
    }

    async copyToClipboard(elementId, type, customContent = null) {
        let textToCopy = '';

        if (customContent) {
            textToCopy = customContent;
        } else if (elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                if (type === 'html') {
                    textToCopy = element.innerHTML;
                } else {
                    textToCopy = element.textContent || element.innerText;
                }
            }
        }

        try {
            await navigator.clipboard.writeText(textToCopy);
            this.showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} copied to clipboard!`, 'success');
        } catch (err) {
            // Fallback for older browsers
            this.fallbackCopyToClipboard(textToCopy);
        }
    }

    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            this.showNotification('Content copied to clipboard!', 'success');
        } catch (err) {
            this.showNotification('Failed to copy content', 'error');
        }

        document.body.removeChild(textArea);
    }

    async copyAsEncoding(elementId, encoding) {
        const element = document.getElementById(elementId);
        if (!element) return;

        let textToCopy = element.textContent || element.innerText;

        try {
            if (encoding === 'shiftjis') {
                // For Shift_JIS, we'll provide a download option since clipboard doesn't support encoding
                this.downloadAsShiftJIS(textToCopy, 'japanese_text.txt');
                this.showNotification('Japanese text prepared for download as Shift_JIS', 'success');
            } else {
                // UTF-8 (default)
                await navigator.clipboard.writeText(textToCopy);
                this.showNotification('Japanese text copied as UTF-8', 'success');
            }
        } catch (err) {
            this.fallbackCopyToClipboard(textToCopy);
        }
    }

    downloadAsShiftJIS(text, filename) {
        try {
            // Create a Blob with the text
            const blob = new Blob([text], { type: 'text/plain;charset=shift_jis' });

            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';

            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error creating Shift_JIS download:', error);
            this.showNotification('Error creating Shift_JIS file', 'error');
        }
    }

    // Enhanced escapeHtml to handle Japanese characters properly
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Enhanced formatFileSize to handle Japanese filenames
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showDebugPanel() {
        let debugPanel = document.getElementById('debugPanel');
        if (!debugPanel) {
            debugPanel = document.createElement('div');
            debugPanel.id = 'debugPanel';
            debugPanel.className = 'debug-panel';
            debugPanel.innerHTML = `
                <div class="debug-header">
                    <h3>Debug Panel</h3>
                    <div class="debug-controls">
                        <button onclick="window.chatApp.clearLogs()">Clear Logs</button>
                        <button onclick="window.chatApp.exportLogs()">Export Logs</button>
                        <button onclick="window.chatApp.hideDebugPanel()">Close</button>
                    </div>
                </div>
                <div class="debug-content">
                    <div class="debug-section">
                        <h4>Connection Status</h4>
                        <div id="debugConnection">Loading...</div>
                    </div>
                    <div class="debug-section">
                        <h4>Recent Logs</h4>
                        <div id="debugLogs" class="debug-logs"></div>
                    </div>
                    <div class="debug-section">
                        <h4>Server Logs</h4>
                        <button onclick="window.chatApp.loadServerLogs()">Load Server Logs</button>
                        <div id="serverLogs" class="debug-logs"></div>
                    </div>
                </div>
            `;
            document.body.appendChild(debugPanel);
        }

        debugPanel.style.display = 'block';
        this.updateDebugPanel();

        // Auto-refresh debug panel every 2 seconds
        this.debugInterval = setInterval(() => {
            this.updateDebugPanel();
        }, 2000);
    }

    hideDebugPanel() {
        const debugPanel = document.getElementById('debugPanel');
        if (debugPanel) {
            debugPanel.style.display = 'none';
        }

        if (this.debugInterval) {
            clearInterval(this.debugInterval);
            this.debugInterval = null;
        }
    }

    updateDebugPanel() {
        const connectionDiv = document.getElementById('debugConnection');
        const logsDiv = document.getElementById('debugLogs');

        if (connectionDiv) {
            connectionDiv.innerHTML = `
                <p>Socket Connected: ${this.isConnected}</p>
                <p>Socket ID: ${this.socket ? this.socket.id : 'N/A'}</p>
                <p>Selected Model: ${this.selectedModel}</p>
                <p>Conversation Length: ${this.conversationHistory.length}</p>
                <p>Uploaded Files: ${this.uploadedFiles.length}</p>
                <p>Current Response Length: ${this.currentResponse.length}</p>
            `;
        }

        if (logsDiv) {
            const recentLogs = this.logs.slice(-20);
            logsDiv.innerHTML = recentLogs.map(log =>
                `<div class="log-entry log-${log.level}">
                    <span class="log-time">${new Date(log.timestamp).toLocaleTimeString()}</span>
                    <span class="log-level">[${log.level.toUpperCase()}]</span>
                    <span class="log-message">${log.message}</span>
                    ${log.data ? `<pre class="log-data">${JSON.stringify(log.data, null, 2)}</pre>` : ''}
                </div>`
            ).join('');

            // Auto-scroll to bottom
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
    }

    clearLogs() {
        this.logs = [];
        localStorage.removeItem('chatAppLogs');
        this.log('info', 'Logs cleared');
        this.updateDebugPanel();
    }

    exportLogs() {
        const logsText = this.logs.map(log =>
            `${log.timestamp} [${log.level.toUpperCase()}] ${log.message}${log.data ? '\n' + JSON.stringify(log.data, null, 2) : ''}`
        ).join('\n');

        const blob = new Blob([logsText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chat-logs-${new Date().toISOString().slice(0, 19)}.txt`;
        a.click();
        URL.revokeObjectURL(url);
    }

    async loadServerLogs() {
        try {
            const response = await fetch('/debug/logs');
            const logs = await response.text();
            const serverLogsDiv = document.getElementById('serverLogs');
            if (serverLogsDiv) {
                serverLogsDiv.innerHTML = `<pre>${logs}</pre>`;
            }
        } catch (error) {
            this.log('error', 'Failed to load server logs', error);
        }
    }

    getFileIcon(fileType, filename) {
        const ext = filename.split('.').pop().toLowerCase();

        switch (fileType) {
            case 'image':
                return 'fa-image';
            case 'document':
                if (ext === 'pdf') return 'fa-file-pdf';
                if (['docx', 'doc'].includes(ext)) return 'fa-file-word';
                if (['xlsx', 'xls'].includes(ext)) return 'fa-file-excel';
                if (['pptx', 'ppt'].includes(ext)) return 'fa-file-powerpoint';
                return 'fa-file-alt';
            case 'text':
                if (ext === 'html' || ext === 'htm') return 'fa-file-code';
                if (ext === 'css') return 'fa-file-code';
                if (ext === 'json') return 'fa-file-code';
                if (ext === 'xml') return 'fa-file-code';
                if (ext === 'csv') return 'fa-file-csv';
                return 'fa-file-alt';
            case 'code':
                return 'fa-file-code';
            default:
                return 'fa-file';
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${this.escapeHtml(message)}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; cursor: pointer; padding: 0.25rem;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        this.elements.notifications.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    disableInput() {
        this.elements.messageInput.disabled = true;
        this.elements.sendButton.disabled = true;
    }

    enableInput() {
        this.elements.messageInput.disabled = false;
        this.elements.sendButton.disabled = false;
    }

    scrollToBottom() {
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Global functions for HTML event handlers
function handleUploadKeydown(event) {
    if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        document.getElementById('fileInput').click();
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatApp = new ChatApp();
});
