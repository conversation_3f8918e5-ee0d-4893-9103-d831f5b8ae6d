// DeepSeek Chat Application JavaScript

class ChatApp {
    constructor() {
        this.socket = null;
        this.conversationHistory = [];
        this.isConnected = false;
        this.currentResponse = '';
        this.uploadedFiles = [];
        
        this.initializeElements();
        this.initializeSocket();
        this.setupEventListeners();
        this.setupFileUpload();
    }

    initializeElements() {
        this.elements = {
            chatMessages: document.getElementById('chatMessages'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendButton'),
            connectionStatus: document.getElementById('connectionStatus'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            uploadArea: document.getElementById('uploadArea'),
            fileInput: document.getElementById('fileInput'),
            uploadProgress: document.getElementById('uploadProgress'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            uploadedFiles: document.getElementById('uploadedFiles'),
            notifications: document.getElementById('notifications'),
            loadingOverlay: document.getElementById('loadingOverlay')
        };
    }

    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.isConnected = true;
            this.updateConnectionStatus('Connected', 'connected');
            this.showNotification('Connected to DeepSeek Chat', 'success');
        });

        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.updateConnectionStatus('Disconnected', 'disconnected');
            this.showNotification('Connection lost. Trying to reconnect...', 'error');
        });

        this.socket.on('status', (data) => {
            this.showNotification(data.message, 'info');
        });

        this.socket.on('message_chunk', (data) => {
            this.handleMessageChunk(data);
        });

        this.socket.on('error', (data) => {
            this.showNotification(data.message, 'error');
            this.enableInput();
        });
    }

    setupEventListeners() {
        // Send button click
        this.elements.sendButton.addEventListener('click', () => this.sendMessage());
        
        // Input auto-resize and enter key handling
        this.elements.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
            this.updateSendButtonState();
        });
        this.elements.messageInput.addEventListener('keydown', (e) => this.handleInputKeydown(e));
        
        // Escape key to clear input
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.elements.messageInput.value = '';
                this.autoResizeTextarea();
            }
        });
    }

    setupFileUpload() {
        const uploadArea = this.elements.uploadArea;
        const fileInput = this.elements.fileInput;

        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // Keyboard support for upload area
        uploadArea.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                fileInput.click();
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleMultipleFileUpload(Array.from(e.target.files));
            }
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleMultipleFileUpload(Array.from(files));
            }
        });
    }

    updateConnectionStatus(text, status) {
        this.elements.statusText.textContent = text;
        this.elements.statusIndicator.className = `status-indicator ${status}`;
    }

    autoResizeTextarea() {
        const textarea = this.elements.messageInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    handleInputKeydown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    }

    sendMessage() {
        const message = this.elements.messageInput.value.trim();
        if (!message || !this.isConnected) return;

        // Get currently uploaded files to include with message
        const attachedFiles = this.uploadedFiles.map(file => file.id);

        // Add user message to chat (show files in UI)
        this.addMessage(message, 'user', attachedFiles);

        // Add to conversation history (without file content for history)
        this.conversationHistory.push({ role: 'user', content: message });

        // Clear input and disable
        this.elements.messageInput.value = '';
        this.autoResizeTextarea();
        this.disableInput();

        // Send to server with attached files
        this.socket.emit('send_message', {
            message: message,
            history: this.conversationHistory,
            files: attachedFiles
        });

        // Clear uploaded files after sending (they're now part of the conversation)
        this.clearUploadedFiles();

        // Start assistant message
        this.startAssistantMessage();
    }

    addMessage(content, sender, attachedFiles = []) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        // Add message text
        const textContent = document.createElement('p');
        textContent.innerHTML = this.escapeHtml(content);
        messageContent.appendChild(textContent);

        // Add attached files display if any
        if (attachedFiles && attachedFiles.length > 0) {
            const filesDiv = document.createElement('div');
            filesDiv.className = 'message-files';

            attachedFiles.forEach(fileId => {
                const fileInfo = this.uploadedFiles.find(f => f.id === fileId);
                if (fileInfo) {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'message-file-item';
                    fileItem.innerHTML = `
                        <i class="fas ${this.getFileIcon(fileInfo.type, fileInfo.filename)}"></i>
                        <span>${this.escapeHtml(fileInfo.filename)}</span>
                        <small>(${this.formatFileSize(fileInfo.size)})</small>
                    `;
                    filesDiv.appendChild(fileItem);
                }
            });

            if (filesDiv.children.length > 0) {
                messageContent.appendChild(filesDiv);
            }
        }

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);

        this.elements.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();

        return messageContent;
    }

    startAssistantMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant-message';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = '<i class="fas fa-robot"></i>';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = '<p></p>';
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        this.elements.chatMessages.appendChild(messageDiv);
        this.currentMessageElement = messageContent.querySelector('p');
        this.currentResponse = '';
        
        this.scrollToBottom();
    }

    handleMessageChunk(data) {
        if (data.is_complete) {
            // Response complete
            this.conversationHistory.push({ role: 'assistant', content: data.full_response });
            this.enableInput();
            this.elements.messageInput.focus();
        } else {
            // Add chunk to current response
            this.currentResponse += data.content;
            if (this.currentMessageElement) {
                this.currentMessageElement.textContent = this.currentResponse;
                this.scrollToBottom();
            }
        }
    }

    async handleMultipleFileUpload(files) {
        // Validate all files first
        const validFiles = [];
        const invalidFiles = [];

        for (const file of files) {
            if (this.validateFile(file)) {
                validFiles.push(file);
            } else {
                invalidFiles.push(file.name);
            }
        }

        if (invalidFiles.length > 0) {
            this.showNotification(`Invalid files: ${invalidFiles.join(', ')}`, 'warning');
        }

        if (validFiles.length === 0) {
            return;
        }

        // Show progress
        this.showUploadProgress(`Uploading ${validFiles.length} file(s)...`);

        const formData = new FormData();
        validFiles.forEach(file => {
            formData.append('files', file);
        });

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // Add all uploaded files
                result.files.forEach(fileInfo => {
                    this.addUploadedFile(fileInfo);
                });

                this.showNotification(result.message, 'success');

                // Show warnings if any
                if (result.warnings && result.warnings.length > 0) {
                    setTimeout(() => {
                        this.showNotification(`Some files failed: ${result.warnings.join('; ')}`, 'warning');
                    }, 1000);
                }
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            this.showNotification('Upload failed: ' + error.message, 'error');
        } finally {
            this.hideUploadProgress();
        }
    }

    // Keep the old method for backward compatibility
    async handleFileUpload(file) {
        return this.handleMultipleFileUpload([file]);
    }

    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            // Images
            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/svg+xml',
            // Documents
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/vnd.ms-powerpoint',
            'application/rtf',
            // Text and code files
            'text/plain', 'text/markdown', 'text/html', 'text/css', 'text/javascript',
            'application/json', 'application/xml', 'text/csv',
            'text/x-python', 'text/x-java-source', 'text/x-c++src', 'text/x-csrc',
            'text/x-chdr', 'text/x-php', 'text/x-ruby', 'text/x-go', 'text/x-rust',
            'text/x-sql', 'text/x-shellscript', 'text/yaml'
        ];

        if (file.size > maxSize) {
            this.showNotification(`File "${file.name}" exceeds 10MB limit`, 'error');
            return false;
        }

        // Check file extension as fallback for MIME type detection
        const fileName = file.name.toLowerCase();
        const allowedExtensions = [
            '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg',
            '.pdf', '.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt', '.rtf',
            '.txt', '.md', '.html', '.htm', '.css', '.js', '.json', '.xml', '.csv', '.log',
            '.py', '.java', '.cpp', '.c', '.h', '.php', '.rb', '.go', '.rs', '.sql', '.sh', '.yaml', '.yml'
        ];

        const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
        const hasValidMimeType = allowedTypes.includes(file.type) || file.type.startsWith('text/');

        if (!hasValidExtension && !hasValidMimeType) {
            this.showNotification(`File type not supported: "${file.name}"`, 'error');
            return false;
        }

        return true;
    }

    showUploadProgress(message = 'Uploading...') {
        this.elements.uploadProgress.style.display = 'block';
        this.elements.progressFill.style.width = '0%';
        this.elements.progressText.textContent = message;

        // Simulate progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 30;
            if (progress >= 90) {
                clearInterval(interval);
                progress = 90;
            }
            this.elements.progressFill.style.width = progress + '%';
        }, 100);

        this.uploadInterval = interval;
    }

    hideUploadProgress() {
        if (this.uploadInterval) {
            clearInterval(this.uploadInterval);
        }
        this.elements.progressFill.style.width = '100%';
        this.elements.progressText.textContent = 'Complete!';
        
        setTimeout(() => {
            this.elements.uploadProgress.style.display = 'none';
        }, 1000);
    }

    addUploadedFile(fileInfo) {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'uploaded-file';
        fileDiv.setAttribute('data-file-type', fileInfo.type || 'other');
        fileDiv.setAttribute('data-file-id', fileInfo.id);

        const thumbnail = document.createElement('div');
        thumbnail.className = 'file-thumbnail';

        if (fileInfo.thumbnail) {
            const img = document.createElement('img');
            img.src = `/uploads/${fileInfo.thumbnail}`;
            img.alt = fileInfo.filename;
            thumbnail.appendChild(img);
        } else {
            // Show appropriate icon based on file type
            const icon = this.getFileIcon(fileInfo.type, fileInfo.filename);
            thumbnail.innerHTML = `<i class="fas ${icon}"></i>`;
        }

        const fileInfoDiv = document.createElement('div');
        fileInfoDiv.className = 'file-info';

        let previewHtml = '';
        if (fileInfo.preview && (fileInfo.type === 'text' || fileInfo.type === 'code')) {
            previewHtml = `<div class="file-preview">${this.escapeHtml(fileInfo.preview)}</div>`;
        }

        fileInfoDiv.innerHTML = `
            <div class="file-name" title="${this.escapeHtml(fileInfo.filename)}">${this.escapeHtml(fileInfo.filename)}</div>
            <div class="file-details">${this.formatFileSize(fileInfo.size)} • ${new Date(fileInfo.upload_time).toLocaleTimeString()} • ${fileInfo.type || 'file'}</div>
            ${previewHtml}
        `;

        // Add remove button
        const removeButton = document.createElement('button');
        removeButton.className = 'file-remove-btn';
        removeButton.innerHTML = '<i class="fas fa-times"></i>';
        removeButton.title = 'Remove file';
        removeButton.onclick = () => this.removeUploadedFile(fileInfo.id);

        fileDiv.appendChild(thumbnail);
        fileDiv.appendChild(fileInfoDiv);
        fileDiv.appendChild(removeButton);

        this.elements.uploadedFiles.appendChild(fileDiv);
        this.uploadedFiles.push(fileInfo);

        // Show indication that files are ready to be sent
        this.updateSendButtonState();
    }

    removeUploadedFile(fileId) {
        // Remove from array
        this.uploadedFiles = this.uploadedFiles.filter(file => file.id !== fileId);

        // Remove from DOM
        const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
        if (fileElement) {
            fileElement.remove();
        }

        this.updateSendButtonState();
    }

    clearUploadedFiles() {
        this.uploadedFiles = [];
        this.elements.uploadedFiles.innerHTML = '';
        this.updateSendButtonState();
    }

    updateSendButtonState() {
        const hasFiles = this.uploadedFiles.length > 0;
        const hasMessage = this.elements.messageInput.value.trim().length > 0;

        if (hasFiles && !hasMessage) {
            this.elements.messageInput.placeholder = 'Ask me about the uploaded files...';
        } else {
            this.elements.messageInput.placeholder = 'Type your message here...';
        }

        // Update send button appearance if files are attached
        if (hasFiles) {
            this.elements.sendButton.classList.add('has-files');
        } else {
            this.elements.sendButton.classList.remove('has-files');
        }
    }

    getFileIcon(fileType, filename) {
        const ext = filename.split('.').pop().toLowerCase();

        switch (fileType) {
            case 'image':
                return 'fa-image';
            case 'document':
                if (ext === 'pdf') return 'fa-file-pdf';
                if (['docx', 'doc'].includes(ext)) return 'fa-file-word';
                if (['xlsx', 'xls'].includes(ext)) return 'fa-file-excel';
                if (['pptx', 'ppt'].includes(ext)) return 'fa-file-powerpoint';
                return 'fa-file-alt';
            case 'text':
                if (ext === 'html' || ext === 'htm') return 'fa-file-code';
                if (ext === 'css') return 'fa-file-code';
                if (ext === 'json') return 'fa-file-code';
                if (ext === 'xml') return 'fa-file-code';
                if (ext === 'csv') return 'fa-file-csv';
                return 'fa-file-alt';
            case 'code':
                return 'fa-file-code';
            default:
                return 'fa-file';
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${this.escapeHtml(message)}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; cursor: pointer; padding: 0.25rem;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        this.elements.notifications.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    disableInput() {
        this.elements.messageInput.disabled = true;
        this.elements.sendButton.disabled = true;
    }

    enableInput() {
        this.elements.messageInput.disabled = false;
        this.elements.sendButton.disabled = false;
    }

    scrollToBottom() {
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Global functions for HTML event handlers
function handleUploadKeydown(event) {
    if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        document.getElementById('fileInput').click();
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatApp = new ChatApp();
});
