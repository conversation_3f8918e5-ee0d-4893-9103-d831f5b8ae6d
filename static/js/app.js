// DeepSeek Chat Application JavaScript

class ChatApp {
    constructor() {
        this.socket = null;
        this.conversationHistory = [];
        this.isConnected = false;
        this.currentResponse = '';
        this.uploadedFiles = [];
        this.selectedModel = 'deepseek-chat'; // Default model

        this.initializeElements();
        this.initializeSocket();
        this.setupEventListeners();
        this.setupFileUpload();
    }

    initializeElements() {
        this.elements = {
            chatMessages: document.getElementById('chatMessages'),
            messageInput: document.getElementById('messageInput'),
            sendButton: document.getElementById('sendButton'),
            connectionStatus: document.getElementById('connectionStatus'),
            statusIndicator: document.getElementById('statusIndicator'),
            statusText: document.getElementById('statusText'),
            attachButton: document.getElementById('attachButton'),
            fileInput: document.getElementById('fileInput'),
            uploadProgress: document.getElementById('uploadProgress'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            uploadedFiles: document.getElementById('uploadedFiles'),
            notifications: document.getElementById('notifications'),
            loadingOverlay: document.getElementById('loadingOverlay'),
            modelSelect: document.getElementById('modelSelect'),
            uploadInfo: document.getElementById('uploadInfo')
        };
    }

    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            this.isConnected = true;
            this.updateConnectionStatus('Connected', 'connected');
            this.showNotification('Connected to DeepSeek Chat', 'success');
        });

        this.socket.on('disconnect', () => {
            this.isConnected = false;
            this.updateConnectionStatus('Disconnected', 'disconnected');
            this.showNotification('Connection lost. Trying to reconnect...', 'error');
        });

        this.socket.on('status', (data) => {
            this.showNotification(data.message, 'info');
        });

        this.socket.on('message_chunk', (data) => {
            this.handleMessageChunk(data);
        });

        this.socket.on('error', (data) => {
            this.showNotification(data.message, 'error');
            this.enableInput();
        });
    }

    setupEventListeners() {
        // Send button click
        this.elements.sendButton.addEventListener('click', () => this.sendMessage());
        
        // Input auto-resize and enter key handling
        this.elements.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
            this.updateSendButtonState();
        });
        this.elements.messageInput.addEventListener('keydown', (e) => this.handleInputKeydown(e));
        
        // Escape key to clear input
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.elements.messageInput.value = '';
                this.autoResizeTextarea();
            }
        });

        // Model selection change
        this.elements.modelSelect.addEventListener('change', (e) => {
            this.selectedModel = e.target.value;
            const modelName = e.target.options[e.target.selectedIndex].text;
            this.showNotification(`Switched to ${modelName}`, 'info');
            this.addModelChangeMessage(modelName);
        });
    }

    setupFileUpload() {
        const attachButton = this.elements.attachButton;
        const fileInput = this.elements.fileInput;
        const inputWrapper = document.querySelector('.input-wrapper');

        // Attach button click
        attachButton.addEventListener('click', () => {
            fileInput.click();
            this.showUploadInfo();
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleMultipleFileUpload(Array.from(e.target.files));
            }
        });

        // Drag and drop on input area
        inputWrapper.addEventListener('dragover', (e) => {
            e.preventDefault();
            inputWrapper.classList.add('drag-over');
        });

        inputWrapper.addEventListener('dragleave', (e) => {
            e.preventDefault();
            inputWrapper.classList.remove('drag-over');
        });

        inputWrapper.addEventListener('drop', (e) => {
            e.preventDefault();
            inputWrapper.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleMultipleFileUpload(Array.from(files));
            }
        });

        // Hide upload info when clicking elsewhere
        document.addEventListener('click', (e) => {
            if (!attachButton.contains(e.target)) {
                this.hideUploadInfo();
            }
        });
    }

    updateConnectionStatus(text, status) {
        this.elements.statusText.textContent = text;
        this.elements.statusIndicator.className = `status-indicator ${status}`;
    }

    autoResizeTextarea() {
        const textarea = this.elements.messageInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    handleInputKeydown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    }

    sendMessage() {
        const message = this.elements.messageInput.value.trim();
        if (!message || !this.isConnected) return;

        // Get currently uploaded files to include with message
        const attachedFiles = this.uploadedFiles.map(file => file.id);

        // Add user message to chat (show files in UI)
        this.addMessage(message, 'user', attachedFiles);

        // Add to conversation history (without file content for history)
        this.conversationHistory.push({ role: 'user', content: message });

        // Clear input and disable
        this.elements.messageInput.value = '';
        this.autoResizeTextarea();
        this.disableInput();

        // Send to server with attached files and selected model
        this.socket.emit('send_message', {
            message: message,
            history: this.conversationHistory,
            files: attachedFiles,
            model: this.selectedModel
        });

        // Clear uploaded files after sending (they're now part of the conversation)
        this.clearUploadedFiles();

        // Start assistant message
        this.startAssistantMessage();
    }

    addMessage(content, sender, attachedFiles = []) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        // Add message text
        const textContent = document.createElement('p');
        textContent.innerHTML = this.escapeHtml(content);
        messageContent.appendChild(textContent);

        // Add attached files display if any
        if (attachedFiles && attachedFiles.length > 0) {
            const filesDiv = document.createElement('div');
            filesDiv.className = 'message-files';

            attachedFiles.forEach(fileId => {
                const fileInfo = this.uploadedFiles.find(f => f.id === fileId);
                if (fileInfo) {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'message-file-item';
                    fileItem.innerHTML = `
                        <i class="fas ${this.getFileIcon(fileInfo.type, fileInfo.filename)}"></i>
                        <span>${this.escapeHtml(fileInfo.filename)}</span>
                        <small>(${this.formatFileSize(fileInfo.size)})</small>
                    `;
                    filesDiv.appendChild(fileItem);
                }
            });

            if (filesDiv.children.length > 0) {
                messageContent.appendChild(filesDiv);
            }
        }

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);

        this.elements.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();

        return messageContent;
    }

    startAssistantMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant-message';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = '<i class="fas fa-robot"></i>';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = '<p></p>';
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        this.elements.chatMessages.appendChild(messageDiv);
        this.currentMessageElement = messageContent.querySelector('p');
        this.currentResponse = '';
        
        this.scrollToBottom();
    }

    handleMessageChunk(data) {
        if (data.is_complete) {
            // Response complete
            this.conversationHistory.push({ role: 'assistant', content: data.full_response });
            this.finalizeAssistantMessage(data.full_response);
            this.enableInput();
            this.elements.messageInput.focus();
        } else {
            // Add chunk to current response
            this.currentResponse += data.content;
            if (this.currentMessageElement) {
                this.currentMessageElement.textContent = this.currentResponse;
                this.scrollToBottom();
            }
        }
    }

    async handleMultipleFileUpload(files) {
        // Validate all files first
        const validFiles = [];
        const invalidFiles = [];

        for (const file of files) {
            if (this.validateFile(file)) {
                validFiles.push(file);
            } else {
                invalidFiles.push(file.name);
            }
        }

        if (invalidFiles.length > 0) {
            this.showNotification(`Invalid files: ${invalidFiles.join(', ')}`, 'warning');
        }

        if (validFiles.length === 0) {
            return;
        }

        // Show progress
        this.showUploadProgress(`Uploading ${validFiles.length} file(s)...`);

        const formData = new FormData();
        validFiles.forEach(file => {
            formData.append('files', file);
        });

        try {
            const response = await fetch('/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                // Add all uploaded files
                result.files.forEach(fileInfo => {
                    this.addUploadedFile(fileInfo);
                });

                this.showNotification(result.message, 'success');

                // Show warnings if any
                if (result.warnings && result.warnings.length > 0) {
                    setTimeout(() => {
                        this.showNotification(`Some files failed: ${result.warnings.join('; ')}`, 'warning');
                    }, 1000);
                }
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            this.showNotification('Upload failed: ' + error.message, 'error');
        } finally {
            this.hideUploadProgress();
        }
    }

    // Keep the old method for backward compatibility
    async handleFileUpload(file) {
        return this.handleMultipleFileUpload([file]);
    }

    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = [
            // Images
            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/svg+xml',
            // Documents
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/vnd.ms-powerpoint',
            'application/rtf',
            // Text and code files
            'text/plain', 'text/markdown', 'text/html', 'text/css', 'text/javascript',
            'application/json', 'application/xml', 'text/csv',
            'text/x-python', 'text/x-java-source', 'text/x-c++src', 'text/x-csrc',
            'text/x-chdr', 'text/x-php', 'text/x-ruby', 'text/x-go', 'text/x-rust',
            'text/x-sql', 'text/x-shellscript', 'text/yaml'
        ];

        if (file.size > maxSize) {
            this.showNotification(`File "${file.name}" exceeds 10MB limit`, 'error');
            return false;
        }

        // Check file extension as fallback for MIME type detection
        const fileName = file.name.toLowerCase();
        const allowedExtensions = [
            '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg',
            '.pdf', '.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt', '.rtf',
            '.txt', '.md', '.html', '.htm', '.css', '.js', '.json', '.xml', '.csv', '.log',
            '.py', '.java', '.cpp', '.c', '.h', '.php', '.rb', '.go', '.rs', '.sql', '.sh', '.yaml', '.yml'
        ];

        const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
        const hasValidMimeType = allowedTypes.includes(file.type) || file.type.startsWith('text/');

        if (!hasValidExtension && !hasValidMimeType) {
            this.showNotification(`File type not supported: "${file.name}"`, 'error');
            return false;
        }

        return true;
    }

    showUploadProgress(message = 'Uploading...') {
        this.elements.uploadProgress.style.display = 'block';
        this.elements.progressFill.style.width = '0%';
        this.elements.progressText.textContent = message;

        // Simulate progress
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 30;
            if (progress >= 90) {
                clearInterval(interval);
                progress = 90;
            }
            this.elements.progressFill.style.width = progress + '%';
        }, 100);

        this.uploadInterval = interval;
    }

    hideUploadProgress() {
        if (this.uploadInterval) {
            clearInterval(this.uploadInterval);
        }
        this.elements.progressFill.style.width = '100%';
        this.elements.progressText.textContent = 'Complete!';
        
        setTimeout(() => {
            this.elements.uploadProgress.style.display = 'none';
        }, 1000);
    }

    addUploadedFile(fileInfo) {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'uploaded-file';
        fileDiv.setAttribute('data-file-type', fileInfo.type || 'other');
        fileDiv.setAttribute('data-file-id', fileInfo.id);

        const thumbnail = document.createElement('div');
        thumbnail.className = 'file-thumbnail';

        if (fileInfo.thumbnail) {
            const img = document.createElement('img');
            img.src = `/uploads/${fileInfo.thumbnail}`;
            img.alt = fileInfo.filename;
            thumbnail.appendChild(img);
        } else {
            // Show appropriate icon based on file type
            const icon = this.getFileIcon(fileInfo.type, fileInfo.filename);
            thumbnail.innerHTML = `<i class="fas ${icon}"></i>`;
        }

        const fileInfoDiv = document.createElement('div');
        fileInfoDiv.className = 'file-info';

        let previewHtml = '';
        if (fileInfo.preview && (fileInfo.type === 'text' || fileInfo.type === 'code')) {
            previewHtml = `<div class="file-preview">${this.escapeHtml(fileInfo.preview)}</div>`;
        }

        // Compact display - just filename and size
        fileInfoDiv.innerHTML = `
            <div class="file-name" title="${this.escapeHtml(fileInfo.filename)}">${this.escapeHtml(fileInfo.filename)}</div>
            <div class="file-details">${this.formatFileSize(fileInfo.size)}</div>
        `;

        // Add remove button
        const removeButton = document.createElement('button');
        removeButton.className = 'file-remove-btn';
        removeButton.innerHTML = '<i class="fas fa-times"></i>';
        removeButton.title = 'Remove file';
        removeButton.onclick = () => this.removeUploadedFile(fileInfo.id);

        fileDiv.appendChild(thumbnail);
        fileDiv.appendChild(fileInfoDiv);
        fileDiv.appendChild(removeButton);

        this.elements.uploadedFiles.appendChild(fileDiv);
        this.uploadedFiles.push(fileInfo);

        // Show indication that files are ready to be sent
        this.updateSendButtonState();
    }

    removeUploadedFile(fileId) {
        // Remove from array
        this.uploadedFiles = this.uploadedFiles.filter(file => file.id !== fileId);

        // Remove from DOM
        const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
        if (fileElement) {
            fileElement.remove();
        }

        this.updateSendButtonState();
    }

    clearUploadedFiles() {
        this.uploadedFiles = [];
        this.elements.uploadedFiles.innerHTML = '';
        this.updateSendButtonState();
    }

    updateSendButtonState() {
        const hasFiles = this.uploadedFiles.length > 0;
        const hasMessage = this.elements.messageInput.value.trim().length > 0;

        if (hasFiles && !hasMessage) {
            this.elements.messageInput.placeholder = 'Ask me about the uploaded files...';
        } else {
            this.elements.messageInput.placeholder = 'Type your message here...';
        }

        // Update attach button appearance if files are attached
        if (hasFiles) {
            this.elements.attachButton.classList.add('has-files');
            this.elements.sendButton.classList.add('has-files');
            this.elements.uploadedFiles.style.display = 'block';
        } else {
            this.elements.attachButton.classList.remove('has-files');
            this.elements.sendButton.classList.remove('has-files');
            this.elements.uploadedFiles.style.display = 'none';
        }
    }

    showUploadInfo() {
        this.elements.uploadInfo.classList.add('show');
    }

    hideUploadInfo() {
        this.elements.uploadInfo.classList.remove('show');
    }

    addModelChangeMessage(modelName) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message system-message';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = `<p><i class="fas fa-cog"></i> Switched to <strong>${this.escapeHtml(modelName)}</strong></p>`;

        messageDiv.appendChild(messageContent);
        this.elements.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    finalizeAssistantMessage(fullResponse) {
        if (!this.currentMessageElement) return;

        // Parse and format the response
        const formattedContent = this.formatResponseContent(fullResponse);

        // Replace the temporary text content with formatted content
        const messageContent = this.currentMessageElement.parentElement;
        messageContent.innerHTML = formattedContent;

        // Add copy button
        this.addCopyButton(messageContent, fullResponse);

        // Apply syntax highlighting if Prism is available
        if (typeof Prism !== 'undefined') {
            Prism.highlightAllUnder(messageContent);
        }

        this.scrollToBottom();
    }

    formatResponseContent(content) {
        // Detect different content types and format accordingly
        const sections = this.parseContentSections(content);
        let formattedHtml = '';

        sections.forEach(section => {
            switch (section.type) {
                case 'html':
                    formattedHtml += this.formatHtmlSection(section.content);
                    break;
                case 'code':
                    formattedHtml += this.formatCodeSection(section.content, section.language);
                    break;
                case 'json':
                    formattedHtml += this.formatJsonSection(section.content);
                    break;
                case 'markdown':
                    formattedHtml += this.formatMarkdownSection(section.content);
                    break;
                default:
                    formattedHtml += this.formatTextSection(section.content);
            }
        });

        return formattedHtml;
    }

    parseContentSections(content) {
        const sections = [];
        const lines = content.split('\n');
        let currentSection = { type: 'text', content: '' };
        let inCodeBlock = false;
        let codeLanguage = '';

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // Detect code blocks
            if (line.startsWith('```')) {
                if (inCodeBlock) {
                    // End of code block
                    sections.push({
                        type: 'code',
                        content: currentSection.content.trim(),
                        language: codeLanguage
                    });
                    currentSection = { type: 'text', content: '' };
                    inCodeBlock = false;
                    codeLanguage = '';
                } else {
                    // Start of code block
                    if (currentSection.content.trim()) {
                        sections.push(currentSection);
                    }
                    codeLanguage = line.substring(3).trim();
                    currentSection = { type: 'code', content: '' };
                    inCodeBlock = true;
                }
                continue;
            }

            if (inCodeBlock) {
                currentSection.content += line + '\n';
            } else {
                // Detect HTML content
                if (line.trim().startsWith('<') && line.trim().endsWith('>')) {
                    if (currentSection.type !== 'html') {
                        if (currentSection.content.trim()) {
                            sections.push(currentSection);
                        }
                        currentSection = { type: 'html', content: '' };
                    }
                }
                // Detect JSON content
                else if ((line.trim().startsWith('{') || line.trim().startsWith('[')) && currentSection.type !== 'json') {
                    if (currentSection.content.trim()) {
                        sections.push(currentSection);
                    }
                    currentSection = { type: 'json', content: '' };
                }

                currentSection.content += line + '\n';
            }
        }

        if (currentSection.content.trim()) {
            sections.push(currentSection);
        }

        return sections;
    }

    formatHtmlSection(content) {
        const sectionId = 'html-' + Math.random().toString(36).substr(2, 9);
        return `
            <div class="content-section html-section">
                <div class="section-header">
                    <span class="section-label"><i class="fab fa-html5"></i> HTML Content</span>
                    <button class="copy-section-btn" onclick="window.chatApp.copyToClipboard('${sectionId}', 'html')" title="Copy HTML">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <div class="html-preview" id="${sectionId}">
                    ${content}
                </div>
                <details class="html-source">
                    <summary>View HTML Source</summary>
                    <pre><code class="language-html">${this.escapeHtml(content)}</code></pre>
                </details>
            </div>
        `;
    }

    formatCodeSection(content, language) {
        const sectionId = 'code-' + Math.random().toString(36).substr(2, 9);
        const languageIcon = this.getLanguageIcon(language);
        return `
            <div class="content-section code-section">
                <div class="section-header">
                    <span class="section-label"><i class="${languageIcon}"></i> ${language.toUpperCase() || 'Code'}</span>
                    <button class="copy-section-btn" onclick="window.chatApp.copyToClipboard('${sectionId}', 'code')" title="Copy Code">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <pre id="${sectionId}"><code class="language-${language}">${this.escapeHtml(content)}</code></pre>
            </div>
        `;
    }

    formatJsonSection(content) {
        const sectionId = 'json-' + Math.random().toString(36).substr(2, 9);
        let formattedJson = content;
        try {
            const parsed = JSON.parse(content);
            formattedJson = JSON.stringify(parsed, null, 2);
        } catch (e) {
            // Keep original if parsing fails
        }

        return `
            <div class="content-section json-section">
                <div class="section-header">
                    <span class="section-label"><i class="fas fa-code"></i> JSON</span>
                    <button class="copy-section-btn" onclick="window.chatApp.copyToClipboard('${sectionId}', 'json')" title="Copy JSON">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <pre id="${sectionId}"><code class="language-json">${this.escapeHtml(formattedJson)}</code></pre>
            </div>
        `;
    }

    formatMarkdownSection(content) {
        const sectionId = 'md-' + Math.random().toString(36).substr(2, 9);
        return `
            <div class="content-section markdown-section">
                <div class="section-header">
                    <span class="section-label"><i class="fab fa-markdown"></i> Markdown</span>
                    <button class="copy-section-btn" onclick="window.chatApp.copyToClipboard('${sectionId}', 'markdown')" title="Copy Markdown">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <div class="markdown-content" id="${sectionId}">${this.parseSimpleMarkdown(content)}</div>
            </div>
        `;
    }

    formatTextSection(content) {
        return `<div class="content-section text-section"><p>${this.escapeHtml(content).replace(/\n/g, '<br>')}</p></div>`;
    }

    getLanguageIcon(language) {
        const icons = {
            'javascript': 'fab fa-js-square',
            'js': 'fab fa-js-square',
            'python': 'fab fa-python',
            'py': 'fab fa-python',
            'html': 'fab fa-html5',
            'css': 'fab fa-css3-alt',
            'java': 'fab fa-java',
            'php': 'fab fa-php',
            'react': 'fab fa-react',
            'vue': 'fab fa-vuejs',
            'angular': 'fab fa-angular',
            'node': 'fab fa-node-js'
        };
        return icons[language.toLowerCase()] || 'fas fa-code';
    }

    parseSimpleMarkdown(content) {
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    addCopyButton(messageContent, fullResponse) {
        const copyButton = document.createElement('button');
        copyButton.className = 'message-copy-btn';
        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
        copyButton.title = 'Copy entire response';
        copyButton.onclick = () => this.copyToClipboard(null, 'full', fullResponse);

        messageContent.appendChild(copyButton);
    }

    async copyToClipboard(elementId, type, customContent = null) {
        let textToCopy = '';

        if (customContent) {
            textToCopy = customContent;
        } else if (elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                if (type === 'html') {
                    textToCopy = element.innerHTML;
                } else {
                    textToCopy = element.textContent || element.innerText;
                }
            }
        }

        try {
            await navigator.clipboard.writeText(textToCopy);
            this.showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} copied to clipboard!`, 'success');
        } catch (err) {
            // Fallback for older browsers
            this.fallbackCopyToClipboard(textToCopy);
        }
    }

    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            this.showNotification('Content copied to clipboard!', 'success');
        } catch (err) {
            this.showNotification('Failed to copy content', 'error');
        }

        document.body.removeChild(textArea);
    }

    getFileIcon(fileType, filename) {
        const ext = filename.split('.').pop().toLowerCase();

        switch (fileType) {
            case 'image':
                return 'fa-image';
            case 'document':
                if (ext === 'pdf') return 'fa-file-pdf';
                if (['docx', 'doc'].includes(ext)) return 'fa-file-word';
                if (['xlsx', 'xls'].includes(ext)) return 'fa-file-excel';
                if (['pptx', 'ppt'].includes(ext)) return 'fa-file-powerpoint';
                return 'fa-file-alt';
            case 'text':
                if (ext === 'html' || ext === 'htm') return 'fa-file-code';
                if (ext === 'css') return 'fa-file-code';
                if (ext === 'json') return 'fa-file-code';
                if (ext === 'xml') return 'fa-file-code';
                if (ext === 'csv') return 'fa-file-csv';
                return 'fa-file-alt';
            case 'code':
                return 'fa-file-code';
            default:
                return 'fa-file';
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${this.escapeHtml(message)}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; cursor: pointer; padding: 0.25rem;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        this.elements.notifications.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    disableInput() {
        this.elements.messageInput.disabled = true;
        this.elements.sendButton.disabled = true;
    }

    enableInput() {
        this.elements.messageInput.disabled = false;
        this.elements.sendButton.disabled = false;
    }

    scrollToBottom() {
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Global functions for HTML event handlers
function handleUploadKeydown(event) {
    if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        document.getElementById('fileInput').click();
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatApp = new ChatApp();
});
