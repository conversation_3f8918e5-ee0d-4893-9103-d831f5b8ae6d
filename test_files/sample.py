#!/usr/bin/env python3
"""
Sample Python file for testing file upload functionality
"""

def hello_world():
    """A simple hello world function"""
    print("Hello, World!")
    return "Hello, World!"

def fibonacci(n):
    """Generate fibonacci sequence up to n terms"""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    fib = [0, 1]
    for i in range(2, n):
        fib.append(fib[i-1] + fib[i-2])
    
    return fib

class Calculator:
    """A simple calculator class"""
    
    def add(self, a, b):
        return a + b
    
    def subtract(self, a, b):
        return a - b
    
    def multiply(self, a, b):
        return a * b
    
    def divide(self, a, b):
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b

if __name__ == "__main__":
    hello_world()
    print("Fibonacci sequence:", fi<PERSON><PERSON><PERSON>(10))
    
    calc = Calculator()
    print("2 + 3 =", calc.add(2, 3))
    print("10 - 4 =", calc.subtract(10, 4))
    print("5 * 6 =", calc.multiply(5, 6))
    print("15 / 3 =", calc.divide(15, 3))
