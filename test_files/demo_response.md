# Demo Response for Testing Formatted Content

This file demonstrates how the chat application will format different types of content in AI responses.

## HTML Content Example

Here's a sample HTML form that the AI might generate:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form</title>
    <style>
        .form-container {
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .submit-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Contact Us</h2>
        <form>
            <div class="form-group">
                <label for="name">Name:</label>
                <input type="text" id="name" name="name" required>
            </div>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="message">Message:</label>
                <textarea id="message" name="message" rows="4" required></textarea>
            </div>
            <button type="submit" class="submit-btn">Send Message</button>
        </form>
    </div>
</body>
</html>
```

## JavaScript Code Example

```javascript
// Form validation and submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const messageInput = document.getElementById('message');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Validate inputs
        if (!validateForm()) {
            return;
        }
        
        // Submit form data
        submitForm({
            name: nameInput.value,
            email: emailInput.value,
            message: messageInput.value
        });
    });

    function validateForm() {
        let isValid = true;
        
        if (nameInput.value.trim() === '') {
            showError(nameInput, 'Name is required');
            isValid = false;
        }
        
        if (!isValidEmail(emailInput.value)) {
            showError(emailInput, 'Valid email is required');
            isValid = false;
        }
        
        if (messageInput.value.trim() === '') {
            showError(messageInput, 'Message is required');
            isValid = false;
        }
        
        return isValid;
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showError(input, message) {
        // Remove existing error
        const existingError = input.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        errorDiv.style.color = 'red';
        errorDiv.style.fontSize = '0.875rem';
        errorDiv.style.marginTop = '5px';
        
        input.parentNode.appendChild(errorDiv);
    }

    function submitForm(data) {
        console.log('Submitting form data:', data);
        alert('Form submitted successfully!');
    }
});
```

## JSON Configuration Example

```json
{
    "formConfig": {
        "title": "Contact Form",
        "fields": [
            {
                "name": "name",
                "type": "text",
                "label": "Full Name",
                "required": true,
                "validation": {
                    "minLength": 2,
                    "maxLength": 50
                }
            },
            {
                "name": "email",
                "type": "email",
                "label": "Email Address",
                "required": true,
                "validation": {
                    "pattern": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$"
                }
            },
            {
                "name": "phone",
                "type": "tel",
                "label": "Phone Number",
                "required": false,
                "validation": {
                    "pattern": "^[\\d\\s\\-\\+\\(\\)]+$"
                }
            },
            {
                "name": "message",
                "type": "textarea",
                "label": "Your Message",
                "required": true,
                "validation": {
                    "minLength": 10,
                    "maxLength": 1000
                }
            }
        ],
        "styling": {
            "theme": "modern",
            "primaryColor": "#007bff",
            "borderRadius": "4px",
            "spacing": "15px"
        },
        "submission": {
            "method": "POST",
            "endpoint": "/api/contact",
            "successMessage": "Thank you for your message!",
            "errorMessage": "Sorry, there was an error sending your message."
        }
    }
}
```

This demonstrates how the chat application will:
1. **Detect HTML content** and render it in a preview box with source code view
2. **Format JavaScript code** with syntax highlighting
3. **Pretty-print JSON** with proper indentation
4. **Add copy buttons** for each section
5. **Provide a main copy button** for the entire response
