こんにちは、世界！

これは日本語のテストファイルです。このファイルは、チャットアプリケーションが日本語のテキストを正しく処理できるかどうかをテストするために作成されました。

## 日本語の文字種

### ひらがな
あいうえお
かきくけこ
さしすせそ
たちつてと
なにぬねの

### カタカナ
アイウエオ
カキクケコ
サシスセソ
タチツテト
ナニヌネノ

### 漢字
日本語には多くの漢字が使用されています。
例：山、川、海、空、雲、花、木、鳥、魚、人

## プログラミング関連の日本語

```python
# 日本語のコメント
def こんにちは():
    print("こんにちは、世界！")
    return "成功"

# 変数名も日本語で書けます
名前 = "田中太郎"
年齢 = 25
職業 = "プログラマー"

print(f"名前: {名前}")
print(f"年齢: {年齢}")
print(f"職業: {職業}")
```

## 技術用語の日本語

- コンピュータ（計算機）
- プログラム（程式）
- データベース（資料庫）
- ネットワーク（網路）
- インターネット（網際網路）
- ソフトウェア（軟體）
- ハードウェア（硬體）

## 文字エンコーディング

このファイルは以下のエンコーディングで保存できます：
- UTF-8（推奨）
- Shift_JIS（日本の従来形式）
- EUC-JP（Unix系で使用）
- ISO-2022-JP（電子メールで使用）

## 特殊文字と記号

日本語では以下のような特殊文字も使用されます：
・中点（なかてん）
「」かぎかっこ
『』二重かぎかっこ
〜波ダッシュ
…三点リーダー

## 数字の表記

漢数字：一、二、三、四、五、六、七、八、九、十
アラビア数字：1、2、3、4、5、6、7、8、9、10

## 日付と時刻

今日は2025年7月15日（令和7年）です。
時刻：午前10時30分

## 敬語の例

- 丁寧語：です、ます
- 尊敬語：いらっしゃる、おっしゃる
- 謙譲語：いたします、申し上げます

このファイルを使用して、日本語テキストの処理、表示、コピー機能をテストしてください。
