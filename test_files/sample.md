# Sample Markdown File

This is a **sample Markdown file** to demonstrate the file upload functionality with text preview.

## Features Demonstrated

### Text Formatting
- **Bold text**
- *Italic text*
- `Inline code`
- ~~Strikethrough text~~

### Lists

#### Unordered List
- Item 1
- Item 2
  - Nested item 2.1
  - Nested item 2.2
- Item 3

#### Ordered List
1. First item
2. Second item
3. Third item

### Code Blocks

```python
def hello_world():
    print("Hello from Markdown!")
    return "Success"
```

```javascript
function greetUser(name) {
    console.log(`Hello, ${name}!`);
    return `Welcome, ${name}`;
}
```

### Tables

| Feature | Status | Description |
|---------|--------|-------------|
| File Upload | ✅ | Multiple file support |
| Drag & Drop | ✅ | Intuitive interface |
| Preview | ✅ | Text file preview |
| Validation | ✅ | Type and size checks |

### Links and Images

- [DeepSeek API Documentation](https://api.deepseek.com)
- [Flask Documentation](https://flask.palletsprojects.com/)
- [Socket.IO Documentation](https://socket.io/docs/)

### Blockquotes

> "The best way to predict the future is to create it."
> 
> — Peter Drucker

### Horizontal Rule

---

## Technical Details

This file demonstrates:
1. **Markdown syntax** rendering
2. **File preview** functionality
3. **Text content** handling
4. **Multiple file** upload support

### File Upload Process

1. User selects or drags files
2. Client validates file types and sizes
3. Files are uploaded to server
4. Server validates MIME types
5. Text files get content preview
6. Images get thumbnail generation
7. Success/error feedback provided

---

*This is a sample file created for testing the enhanced file upload functionality.*
