{"name": "DeepSeek Chat Application", "version": "2.0.0", "description": "A modern web application for AI chat with file upload capabilities", "features": ["Real-time streaming chat", "Multiple file upload support", "Drag and drop functionality", "File type validation", "Thumbnail generation", "Text file preview", "Responsive design", "Accessibility features"], "supported_file_types": {"images": ["jpg", "jpeg", "png", "gif", "webp", "bmp", "svg"], "documents": ["pdf", "docx", "doc", "xlsx", "xls", "pptx", "ppt", "rtf"], "text": ["txt", "md", "html", "htm", "css", "js", "json", "xml", "csv", "log"], "code": ["py", "java", "cpp", "c", "h", "php", "rb", "go", "rs", "sql", "sh", "yaml", "yml"]}, "configuration": {"max_file_size": "10MB", "multiple_uploads": true, "thumbnail_size": "150x150", "preview_length": 500}, "api": {"provider": "DeepSeek", "model": "deepseek-chat", "temperature": 0.7, "max_tokens": 1000, "streaming": true}, "technology_stack": {"backend": {"framework": "Flask", "websockets": "Flask-SocketIO", "file_handling": "python-magic", "image_processing": "Pillow"}, "frontend": {"html": "HTML5", "css": "CSS3 with Grid and Flexbox", "javascript": "Vanilla ES6+", "websockets": "Socket.IO"}}, "accessibility": {"aria_labels": true, "keyboard_navigation": true, "screen_reader_support": true, "high_contrast_mode": true, "semantic_html": true}, "testing": {"browsers": ["Chrome 80+", "Firefox 75+", "Safari 13+", "Edge 80+"], "mobile_support": true, "responsive_breakpoints": ["768px", "1200px"]}}