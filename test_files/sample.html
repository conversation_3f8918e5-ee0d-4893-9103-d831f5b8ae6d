<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample HTML File</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: #007bff;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        .content {
            margin: 20px 0;
        }
        .button {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sample HTML Document</h1>
        <p>This is a test file for the file upload functionality</p>
    </div>
    
    <div class="content">
        <h2>Features</h2>
        <ul>
            <li>HTML structure demonstration</li>
            <li>CSS styling included</li>
            <li>JavaScript functionality</li>
            <li>Responsive design elements</li>
        </ul>
        
        <h2>Interactive Elements</h2>
        <button class="button" onclick="showAlert()">Click Me!</button>
        <p id="output">Click the button to see a message.</p>
    </div>
    
    <script>
        function showAlert() {
            document.getElementById('output').innerHTML = 
                '<strong>Hello!</strong> This is a sample HTML file with JavaScript.';
        }
        
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Sample HTML file loaded successfully!');
        });
    </script>
</body>
</html>
