2025-07-15 00:25:10,152 - werkzeug - WARNING -  * Debugger is active!
2025-07-15 00:25:10,152 - werkzeug - INFO -  * Debugger PIN: 527-369-049
2025-07-15 00:25:12,174 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:25:12] "GET /socket.io/?EIO=4&transport=polling&t=PW9-1mi HTTP/1.1" 200 -
2025-07-15 00:25:12,429 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:25:12] "POST /socket.io/?EIO=4&transport=polling&t=PW9-1rn&sid=gXVej1rxrlF2fUbGAAAA HTTP/1.1" 200 -
2025-07-15 00:25:12,486 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:25:12] "GET /socket.io/?EIO=4&transport=polling&t=PW9-1ro&sid=gXVej1rxrlF2fUbGAAAA HTTP/1.1" 200 -
2025-07-15 00:25:12,492 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:25:12] "GET /socket.io/?EIO=4&transport=polling&t=PW9-1wf&sid=gXVej1rxrlF2fUbGAAAA HTTP/1.1" 200 -
2025-07-15 00:25:32,560 - werkzeug - INFO -  * Detected change in '/home/<USER>/work/python/chat-ui/app.py', reloading
2025-07-15 00:25:33,376 - werkzeug - WARNING -  * Debugger is active!
2025-07-15 00:25:33,376 - werkzeug - INFO -  * Debugger PIN: 527-369-049
2025-07-15 00:25:35,164 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:25:35] "GET /socket.io/?EIO=4&transport=polling&t=PW9-7O4 HTTP/1.1" 200 -
2025-07-15 00:25:35,419 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:25:35] "POST /socket.io/?EIO=4&transport=polling&t=PW9-7S_&sid=XzSjTXW3CoegZKswAAAA HTTP/1.1" 200 -
2025-07-15 00:25:35,481 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:25:35] "GET /socket.io/?EIO=4&transport=polling&t=PW9-7T0&sid=XzSjTXW3CoegZKswAAAA HTTP/1.1" 200 -
2025-07-15 00:25:35,494 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:25:35] "GET /socket.io/?EIO=4&transport=polling&t=PW9-7Y1&sid=XzSjTXW3CoegZKswAAAA HTTP/1.1" 200 -
2025-07-15 00:26:00,875 - werkzeug - INFO -  * Detected change in '/home/<USER>/work/python/chat-ui/app.py', reloading
2025-07-15 00:26:01,760 - werkzeug - WARNING -  * Debugger is active!
2025-07-15 00:26:01,761 - werkzeug - INFO -  * Debugger PIN: 527-369-049
2025-07-15 00:26:03,164 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:03] "GET /socket.io/?EIO=4&transport=polling&t=PW9-EDY HTTP/1.1" 200 -
2025-07-15 00:26:03,433 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:03] "POST /socket.io/?EIO=4&transport=polling&t=PW9-EIV&sid=SxdR4TUMVEf2caPyAAAA HTTP/1.1" 200 -
2025-07-15 00:26:03,479 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:03] "GET /socket.io/?EIO=4&transport=polling&t=PW9-EIV.0&sid=SxdR4TUMVEf2caPyAAAA HTTP/1.1" 200 -
2025-07-15 00:26:03,500 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:03] "GET /socket.io/?EIO=4&transport=polling&t=PW9-ENa&sid=SxdR4TUMVEf2caPyAAAA HTTP/1.1" 200 -
2025-07-15 00:26:14,960 - werkzeug - INFO -  * Detected change in '/home/<USER>/work/python/chat-ui/app.py', reloading
2025-07-15 00:26:15,712 - werkzeug - WARNING -  * Debugger is active!
2025-07-15 00:26:15,712 - werkzeug - INFO -  * Debugger PIN: 527-369-049
2025-07-15 00:26:17,162 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:17] "GET /socket.io/?EIO=4&transport=polling&t=PW9-HeH HTTP/1.1" 200 -
2025-07-15 00:26:17,423 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:17] "POST /socket.io/?EIO=4&transport=polling&t=PW9-HjE&sid=DG1s3jY2CNXBFI9VAAAA HTTP/1.1" 200 -
2025-07-15 00:26:17,485 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:17] "GET /socket.io/?EIO=4&transport=polling&t=PW9-HjF&sid=DG1s3jY2CNXBFI9VAAAA HTTP/1.1" 200 -
2025-07-15 00:26:17,493 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:17] "GET /socket.io/?EIO=4&transport=polling&t=PW9-HoG&sid=DG1s3jY2CNXBFI9VAAAA HTTP/1.1" 200 -
2025-07-15 00:26:28,915 - werkzeug - INFO -  * Detected change in '/home/<USER>/work/python/chat-ui/app.py', reloading
2025-07-15 00:26:29,695 - werkzeug - WARNING -  * Debugger is active!
2025-07-15 00:26:29,696 - werkzeug - INFO -  * Debugger PIN: 527-369-049
2025-07-15 00:26:30,162 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:30] "GET /socket.io/?EIO=4&transport=polling&t=PW9-KpR HTTP/1.1" 200 -
2025-07-15 00:26:30,425 - __main__ - INFO - Client connected: gsdGNh8_6fla4B6IAAAB
2025-07-15 00:26:30,425 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:30] "POST /socket.io/?EIO=4&transport=polling&t=PW9-KuM&sid=zZdmNI88tdoOUooaAAAA HTTP/1.1" 200 -
2025-07-15 00:26:30,475 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:30] "GET /socket.io/?EIO=4&transport=polling&t=PW9-KuN&sid=zZdmNI88tdoOUooaAAAA HTTP/1.1" 200 -
2025-07-15 00:26:30,488 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:26:30] "GET /socket.io/?EIO=4&transport=polling&t=PW9-KzG&sid=zZdmNI88tdoOUooaAAAA HTTP/1.1" 200 -
2025-07-15 00:27:00,379 - werkzeug - INFO -  * Detected change in '/home/<USER>/work/python/chat-ui/app.py', reloading
2025-07-15 00:27:01,301 - werkzeug - WARNING -  * Debugger is active!
2025-07-15 00:27:01,301 - werkzeug - INFO -  * Debugger PIN: 527-369-049
2025-07-15 00:27:02,165 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:27:02] "GET /socket.io/?EIO=4&transport=polling&t=PW9-SdP HTTP/1.1" 200 -
2025-07-15 00:27:02,423 - __main__ - INFO - Client connected: KboFdku3pBzVkc3gAAAB
2025-07-15 00:27:02,424 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:27:02] "POST /socket.io/?EIO=4&transport=polling&t=PW9-SiO&sid=xVAAg38IZnXQjji-AAAA HTTP/1.1" 200 -
2025-07-15 00:27:02,489 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:27:02] "GET /socket.io/?EIO=4&transport=polling&t=PW9-SiP&sid=xVAAg38IZnXQjji-AAAA HTTP/1.1" 200 -
2025-07-15 00:27:02,505 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:27:02] "GET /socket.io/?EIO=4&transport=polling&t=PW9-SnZ&sid=xVAAg38IZnXQjji-AAAA HTTP/1.1" 200 -
2025-07-15 00:31:45,267 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-15 00:31:45,268 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-15 00:31:45,268 - werkzeug - INFO -  * Restarting with stat
2025-07-15 00:31:45,857 - werkzeug - WARNING -  * Debugger is active!
2025-07-15 00:31:45,857 - werkzeug - INFO -  * Debugger PIN: 527-369-049
2025-07-15 00:31:49,175 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:49] "GET /socket.io/?EIO=4&transport=polling&t=PW9_Yhn HTTP/1.1" 200 -
2025-07-15 00:31:49,414 - __main__ - INFO - Client connected: _gbVPAsPZS0IFhn_AAAB
2025-07-15 00:31:49,415 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:49] "POST /socket.io/?EIO=4&transport=polling&t=PW9_Yn2&sid=BmsxliB0dOhlpKyDAAAA HTTP/1.1" 200 -
2025-07-15 00:31:49,496 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:49] "GET /socket.io/?EIO=4&transport=polling&t=PW9_Yn5&sid=BmsxliB0dOhlpKyDAAAA HTTP/1.1" 200 -
2025-07-15 00:31:49,576 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:49] "GET / HTTP/1.1" 200 -
2025-07-15 00:31:49,929 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:49] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-15 00:31:49,934 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:49] "GET /socket.io/?EIO=4&transport=polling&t=PW9_YtF&sid=BmsxliB0dOhlpKyDAAAA HTTP/1.1" 200 -
2025-07-15 00:31:49,955 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:49] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-15 00:31:50,167 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:50] "GET /socket.io/?EIO=4&transport=polling&t=PW9_Y_J HTTP/1.1" 200 -
2025-07-15 00:31:50,419 - __main__ - INFO - Client connected: 5xacsdF4jVfDClidAAAD
2025-07-15 00:31:50,421 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:50] "POST /socket.io/?EIO=4&transport=polling&t=PW9_Z0W&sid=77GDmX-ZI45zM0q3AAAC HTTP/1.1" 200 -
2025-07-15 00:31:50,493 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:50] "GET /socket.io/?EIO=4&transport=polling&t=PW9_Z0Y&sid=77GDmX-ZI45zM0q3AAAC HTTP/1.1" 200 -
2025-07-15 00:31:50,517 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:50] "GET /socket.io/?EIO=4&transport=polling&t=PW9_Z5k&sid=77GDmX-ZI45zM0q3AAAC HTTP/1.1" 200 -
2025-07-15 00:31:51,048 - __main__ - INFO - Client disconnected: _gbVPAsPZS0IFhn_AAAB
2025-07-15 00:31:51,049 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:31:51] "GET /socket.io/?EIO=4&transport=websocket&sid=BmsxliB0dOhlpKyDAAAA HTTP/1.1" 200 -
2025-07-15 00:32:13,269 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:32:13] "POST /upload HTTP/1.1" 200 -
2025-07-15 00:32:14,593 - __main__ - INFO - Received message: model=deepseek-chat, message_length=1023, files=3
2025-07-15 00:32:14,594 - __main__ - INFO - Full message length with files: 25589
2025-07-15 00:32:14,594 - __main__ - INFO - Prepared 3 messages for API call
2025-07-15 00:32:14,665 - __main__ - INFO - Starting API call: model=deepseek-chat, temp=0.7, max_tokens=1000
2025-07-15 00:32:14,665 - __main__ - INFO - Streaming thread started
2025-07-15 00:32:15,199 - httpx - INFO - HTTP Request: POST https://api.deepseek.com/chat/completions "HTTP/1.1 200 OK"
2025-07-15 00:32:15,199 - __main__ - INFO - API stream created successfully
2025-07-15 00:32:35,898 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:32:35] "GET /debug/logs HTTP/1.1" 200 -
2025-07-15 00:32:43,769 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:32:43] "GET /debug/logs HTTP/1.1" 200 -
2025-07-15 00:32:58,397 - werkzeug - INFO - 127.0.0.1 - - [15/Jul/2025 00:32:58] "GET /debug/logs HTTP/1.1" 200 -
2025-07-15 00:33:09,338 - __main__ - INFO - Stream finished with reason: length
2025-07-15 00:33:09,338 - __main__ - INFO - Stream completed: 994 chunks, 3187 characters
