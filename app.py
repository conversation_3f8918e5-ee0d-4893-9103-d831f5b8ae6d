#!/usr/bin/env python3
"""
DeepSeek Chat Web Application

A Flask-based web application for streaming chat with file upload capabilities.
"""

import os
import json
import magic
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session
from flask_socketio import SocketIO, emit
from werkzeug.utils import secure_filename
from PIL import Image
from openai import OpenAI
from dotenv import load_dotenv
import uuid
import threading
import time

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024  # 10MB max file size

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*")

# Supported file types and their MIME types
ALLOWED_EXTENSIONS = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg', 
    'png': 'image/png',
    'gif': 'image/gif',
    'pdf': 'application/pdf',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'txt': 'text/plain',
    'md': 'text/markdown'
}

# Create upload directory if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

def create_deepseek_client():
    """Initialize the OpenAI client with DeepSeek API credentials"""
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        raise ValueError("DEEPSEEK_API_KEY environment variable is required")
    
    return OpenAI(
        api_key=api_key,
        base_url="https://api.deepseek.com"
    )

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_file_type(file_path):
    """Validate file type using python-magic"""
    try:
        mime_type = magic.from_file(file_path, mime=True)
        file_ext = file_path.rsplit('.', 1)[1].lower()
        expected_mime = ALLOWED_EXTENSIONS.get(file_ext)
        return mime_type == expected_mime or mime_type.startswith('text/')
    except Exception:
        return False

def create_thumbnail(file_path, filename):
    """Create thumbnail for image files"""
    try:
        if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
            with Image.open(file_path) as img:
                img.thumbnail((150, 150))
                thumb_path = os.path.join(app.config['UPLOAD_FOLDER'], f"thumb_{filename}")
                img.save(thumb_path)
                return f"thumb_{filename}"
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
    return None

@app.route('/')
def index():
    """Main chat interface"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file selected'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not supported'}), 400
        
        # Generate unique filename
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        
        # Save file
        file.save(file_path)
        
        # Validate file type
        if not validate_file_type(file_path):
            os.remove(file_path)
            return jsonify({'error': 'Invalid file type'}), 400
        
        # Create thumbnail if it's an image
        thumbnail = create_thumbnail(file_path, unique_filename)
        
        file_info = {
            'id': str(uuid.uuid4()),
            'filename': filename,
            'unique_filename': unique_filename,
            'size': os.path.getsize(file_path),
            'upload_time': datetime.now().isoformat(),
            'thumbnail': thumbnail
        }
        
        return jsonify({
            'success': True,
            'file': file_info,
            'message': 'File uploaded successfully'
        })
        
    except Exception as e:
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print(f"Client connected: {request.sid}")
    emit('status', {'message': 'Connected to DeepSeek Chat'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print(f"Client disconnected: {request.sid}")

@socketio.on('send_message')
def handle_message(data):
    """Handle incoming chat message and stream response"""
    try:
        user_message = data.get('message', '').strip()
        conversation_history = data.get('history', [])

        if not user_message:
            emit('error', {'message': 'Empty message'})
            return

        # Add user message to history
        messages = [{"role": "system", "content": "You are a helpful assistant. Be concise and friendly."}]
        messages.extend(conversation_history)
        messages.append({"role": "user", "content": user_message})

        # Create DeepSeek client and stream response
        client = create_deepseek_client()

        # Capture the session ID before starting the thread
        session_id = request.sid

        def stream_response():
            try:
                stream = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=messages,
                    stream=True,
                    temperature=0.7,
                    max_tokens=1000
                )

                full_response = ""
                for chunk in stream:
                    if chunk.choices[0].delta.content is not None:
                        content = chunk.choices[0].delta.content
                        full_response += content
                        socketio.emit('message_chunk', {
                            'content': content,
                            'is_complete': False
                        }, room=session_id)
                        time.sleep(0.01)  # Small delay for better UX

                # Send completion signal
                socketio.emit('message_chunk', {
                    'content': '',
                    'is_complete': True,
                    'full_response': full_response
                }, room=session_id)

            except Exception as e:
                socketio.emit('error', {
                    'message': f'Error generating response: {str(e)}'
                }, room=session_id)

        # Start streaming in a separate thread
        thread = threading.Thread(target=stream_response)
        thread.daemon = True
        thread.start()

    except Exception as e:
        emit('error', {'message': f'Error processing message: {str(e)}'})

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
