#!/usr/bin/env python3
"""
DeepSeek Chat Web Application

A Flask-based web application for streaming chat with file upload capabilities.
"""

import os
import json
import magic
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session
from flask_socketio import SocketIO, emit
from werkzeug.utils import secure_filename
from PIL import Image
from openai import OpenAI
from dotenv import load_dotenv
import uuid
import threading
import time

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024  # 10MB max file size

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*")

# Supported file types and their MIME types
ALLOWED_EXTENSIONS = {
    # Images
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'bmp': 'image/bmp',
    'svg': 'image/svg+xml',

    # Documents
    'pdf': 'application/pdf',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'doc': 'application/msword',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xls': 'application/vnd.ms-excel',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'ppt': 'application/vnd.ms-powerpoint',

    # Text files
    'txt': 'text/plain',
    'md': 'text/markdown',
    'html': 'text/html',
    'htm': 'text/html',
    'css': 'text/css',
    'js': 'text/javascript',
    'json': 'application/json',
    'xml': 'application/xml',
    'csv': 'text/csv',
    'rtf': 'application/rtf',
    'log': 'text/plain',

    # Code files
    'py': 'text/x-python',
    'java': 'text/x-java-source',
    'cpp': 'text/x-c++src',
    'c': 'text/x-csrc',
    'h': 'text/x-chdr',
    'php': 'text/x-php',
    'rb': 'text/x-ruby',
    'go': 'text/x-go',
    'rs': 'text/x-rust',
    'sql': 'text/x-sql',
    'sh': 'text/x-shellscript',
    'yaml': 'text/yaml',
    'yml': 'text/yaml'
}

# Create upload directory if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Store file metadata in memory (in production, use a database)
uploaded_files_store = {}

def create_deepseek_client():
    """Initialize the OpenAI client with DeepSeek API credentials"""
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        raise ValueError("DEEPSEEK_API_KEY environment variable is required")
    
    return OpenAI(
        api_key=api_key,
        base_url="https://api.deepseek.com"
    )

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_file_type(file_path):
    """Validate file type using python-magic"""
    try:
        mime_type = magic.from_file(file_path, mime=True)
        file_ext = file_path.rsplit('.', 1)[1].lower()
        expected_mime = ALLOWED_EXTENSIONS.get(file_ext)

        # Direct match
        if mime_type == expected_mime:
            return True

        # Allow text files with various MIME types
        text_extensions = ['txt', 'md', 'html', 'htm', 'css', 'js', 'json', 'xml', 'csv', 'log',
                          'py', 'java', 'cpp', 'c', 'h', 'php', 'rb', 'go', 'rs', 'sql', 'sh', 'yaml', 'yml']
        if file_ext in text_extensions and (mime_type.startswith('text/') or mime_type == 'application/json' or mime_type == 'application/xml'):
            return True

        # Allow some flexibility for common document types
        if file_ext == 'rtf' and 'rtf' in mime_type:
            return True

        return False
    except Exception as e:
        print(f"File validation error: {e}")
        return False

def create_thumbnail(file_path, filename):
    """Create thumbnail for image files"""
    try:
        if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp')):
            with Image.open(file_path) as img:
                img.thumbnail((150, 150))
                thumb_path = os.path.join(app.config['UPLOAD_FOLDER'], f"thumb_{filename}")
                img.save(thumb_path)
                return f"thumb_{filename}"
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
    return None

def get_file_preview(file_path, filename):
    """Get preview content for text files"""
    try:
        text_extensions = ['txt', 'md', 'html', 'htm', 'css', 'js', 'json', 'xml', 'csv', 'log',
                          'py', 'java', 'cpp', 'c', 'h', 'php', 'rb', 'go', 'rs', 'sql', 'sh', 'yaml', 'yml']

        file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

        if file_ext in text_extensions:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read(500)  # First 500 characters
                return content if len(content) <= 500 else content + '...'
    except Exception as e:
        print(f"Error reading file preview: {e}")
    return None

def get_file_type(filename):
    """Determine file type category"""
    file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

    image_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg']
    document_exts = ['pdf', 'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'rtf']
    text_exts = ['txt', 'md', 'html', 'htm', 'css', 'js', 'json', 'xml', 'csv', 'log']
    code_exts = ['py', 'java', 'cpp', 'c', 'h', 'php', 'rb', 'go', 'rs', 'sql', 'sh', 'yaml', 'yml']

    if file_ext in image_exts:
        return 'image'
    elif file_ext in document_exts:
        return 'document'
    elif file_ext in text_exts:
        return 'text'
    elif file_ext in code_exts:
        return 'code'
    else:
        return 'other'

def get_full_file_content(file_path, filename):
    """Get full content of text/code files for AI analysis"""
    try:
        text_extensions = ['txt', 'md', 'html', 'htm', 'css', 'js', 'json', 'xml', 'csv', 'log',
                          'py', 'java', 'cpp', 'c', 'h', 'php', 'rb', 'go', 'rs', 'sql', 'sh', 'yaml', 'yml']

        file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

        if file_ext in text_extensions:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                return content
    except Exception as e:
        print(f"Error reading full file content: {e}")
    return None

def format_files_for_ai(file_ids):
    """Format uploaded files content for AI context"""
    if not file_ids:
        return ""

    files_content = []

    for file_id in file_ids:
        if file_id in uploaded_files_store:
            file_info = uploaded_files_store[file_id]
            file_path = file_info['file_path']
            filename = file_info['filename']
            file_type = file_info['type']

            # Get file content if it's a text file
            content = get_full_file_content(file_path, filename)
            if content:
                files_content.append(f"""
--- FILE: {filename} (Type: {file_type}) ---
{content}
--- END OF FILE: {filename} ---
""")
            else:
                # For non-text files, just mention the file
                files_content.append(f"[Uploaded file: {filename} (Type: {file_type}) - Content not readable as text]")

    if files_content:
        return "\n\nUploaded files:\n" + "\n".join(files_content)
    return ""

@app.route('/')
def index():
    """Main chat interface"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle single or multiple file uploads"""
    try:
        uploaded_files = []
        errors = []

        # Handle multiple files
        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'error': 'No files selected'}), 400

        for file in files:
            if file.filename == '':
                continue

            try:
                if not allowed_file(file.filename):
                    errors.append(f'{file.filename}: File type not supported')
                    continue

                # Generate unique filename
                filename = secure_filename(file.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

                # Save file
                file.save(file_path)

                # Validate file type
                if not validate_file_type(file_path):
                    os.remove(file_path)
                    errors.append(f'{filename}: Invalid file type')
                    continue

                # Create thumbnail if it's an image
                thumbnail = create_thumbnail(file_path, unique_filename)

                # Get file content preview for text files
                content_preview = get_file_preview(file_path, filename)

                file_id = str(uuid.uuid4())
                file_info = {
                    'id': file_id,
                    'filename': filename,
                    'unique_filename': unique_filename,
                    'file_path': file_path,
                    'size': os.path.getsize(file_path),
                    'upload_time': datetime.now().isoformat(),
                    'thumbnail': thumbnail,
                    'preview': content_preview,
                    'type': get_file_type(filename)
                }

                # Store file info for later retrieval
                uploaded_files_store[file_id] = file_info

                uploaded_files.append(file_info)

            except Exception as e:
                errors.append(f'{file.filename}: Upload failed - {str(e)}')

        if not uploaded_files and errors:
            return jsonify({'error': '; '.join(errors)}), 400

        response_data = {
            'success': True,
            'files': uploaded_files,
            'message': f'Successfully uploaded {len(uploaded_files)} file(s)'
        }

        if errors:
            response_data['warnings'] = errors
            response_data['message'] += f' ({len(errors)} failed)'

        return jsonify(response_data)

    except Exception as e:
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    print(f"Client connected: {request.sid}")
    emit('status', {'message': 'Connected to DeepSeek Chat'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print(f"Client disconnected: {request.sid}")

@socketio.on('send_message')
def handle_message(data):
    """Handle incoming chat message and stream response"""
    try:
        user_message = data.get('message', '').strip()
        conversation_history = data.get('history', [])
        attached_files = data.get('files', [])  # Get attached file IDs

        if not user_message:
            emit('error', {'message': 'Empty message'})
            return

        # Include file content in the message if files are attached
        files_context = format_files_for_ai(attached_files)
        full_user_message = user_message + files_context

        # Add user message to history
        messages = [{"role": "system", "content": "You are a helpful assistant. Be concise and friendly. When analyzing uploaded files, provide detailed and helpful analysis of the content."}]
        messages.extend(conversation_history)
        messages.append({"role": "user", "content": full_user_message})

        # Create DeepSeek client and stream response
        client = create_deepseek_client()

        # Capture the session ID before starting the thread
        session_id = request.sid

        def stream_response():
            try:
                stream = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=messages,
                    stream=True,
                    temperature=0.7,
                    max_tokens=1000
                )

                full_response = ""
                for chunk in stream:
                    if chunk.choices[0].delta.content is not None:
                        content = chunk.choices[0].delta.content
                        full_response += content
                        socketio.emit('message_chunk', {
                            'content': content,
                            'is_complete': False
                        }, room=session_id)
                        time.sleep(0.01)  # Small delay for better UX

                # Send completion signal
                socketio.emit('message_chunk', {
                    'content': '',
                    'is_complete': True,
                    'full_response': full_response
                }, room=session_id)

            except Exception as e:
                socketio.emit('error', {
                    'message': f'Error generating response: {str(e)}'
                }, room=session_id)

        # Start streaming in a separate thread
        thread = threading.Thread(target=stream_response)
        thread.daemon = True
        thread.start()

    except Exception as e:
        emit('error', {'message': f'Error processing message: {str(e)}'})

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
