<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ải chat</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header" role="banner">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-robot" aria-hidden="true"></i>
                    ải chat
                </h1>
                <div class="connection-status" id="connectionStatus" aria-live="polite">
                    <span class="status-indicator" id="statusIndicator"></span>
                    <span class="status-text" id="statusText">Connecting...</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content" role="main">
            <!-- Chat Container -->
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages" role="log" aria-live="polite" aria-label="Chat conversation">
                    <div class="welcome-message">
                        <div class="message assistant-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot" aria-hidden="true"></i>
                            </div>
                            <div class="message-content">
                                <p>😒😒😒😒</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- File Upload Area -->
                <div class="file-upload-section" id="fileUploadSection">
                    <div class="upload-area" id="uploadArea" 
                         role="button" 
                         tabindex="0"
                         aria-label="Click to upload files or drag and drop files here"
                         onkeydown="handleUploadKeydown(event)">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt upload-icon" aria-hidden="true"></i>
                            <p class="upload-text">
                                <strong>Click to upload</strong> or drag and drop files here
                            </p>
                            <p class="upload-info">
                                <strong>Images:</strong> JPG, PNG, GIF, WebP, BMP, SVG<br>
                                <strong>Documents:</strong> PDF, DOCX, DOC, XLSX, XLS, PPTX, PPT<br>
                                <strong>Text/Code:</strong> TXT, MD, HTML, CSS, JS, JSON, XML, CSV, PY, JAVA, CPP, etc.<br>
                                <strong>Multiple files supported</strong> • Max 10MB each
                            </p>
                        </div>
                        <input type="file"
                               id="fileInput"
                               class="file-input"
                               multiple
                               accept=".jpg,.jpeg,.png,.gif,.webp,.bmp,.svg,.pdf,.docx,.doc,.xlsx,.xls,.pptx,.ppt,.txt,.md,.html,.htm,.css,.js,.json,.xml,.csv,.rtf,.log,.py,.java,.cpp,.c,.h,.php,.rb,.go,.rs,.sql,.sh,.yaml,.yml"
                               aria-describedby="uploadInfo">
                    </div>
                    
                    <!-- Upload Progress -->
                    <div class="upload-progress" id="uploadProgress" style="display: none;" role="progressbar" aria-valuemin="0" aria-valuemax="100">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <span class="progress-text" id="progressText">Uploading...</span>
                    </div>

                    <!-- Uploaded Files -->
                    <div class="uploaded-files" id="uploadedFiles" aria-label="Uploaded files"></div>
                </div>

                <!-- Input Area -->
                <div class="input-area">
                    <div class="input-container">
                        <textarea 
                            id="messageInput" 
                            class="message-input" 
                            placeholder="Type your message here..." 
                            rows="1"
                            aria-label="Message input"
                            onkeydown="handleInputKeydown(event)"></textarea>
                        <button 
                            id="sendButton" 
                            class="send-button" 
                            type="button"
                            aria-label="Send message"
                            onclick="sendMessage()">
                            <i class="fas fa-paper-plane" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Notifications -->
        <div class="notifications" id="notifications" aria-live="assertive" aria-atomic="true"></div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;" role="status" aria-label="Loading">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin" aria-hidden="true"></i>
                <p>Processing...</p>
            </div>
        </div>
    </div>

    <!-- Help Modal --

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
