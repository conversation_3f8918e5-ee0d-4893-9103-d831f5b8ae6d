<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ải chat</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header" role="banner">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-robot" aria-hidden="true"></i>
                    ải chat
                </h1>
                <div class="header-controls">
                    <div class="model-selector">
                        <label for="modelSelect" class="model-label">Model:</label>
                        <select id="modelSelect" class="model-select" aria-label="Select AI model">
                            <option value="deepseek-chat">DeepSeek Chat</option>
                            <option value="deepseek-reasoner">DeepSeek Reasoner</option>
                        </select>
                    </div>
                    <div class="connection-status" id="connectionStatus" aria-live="polite">
                        <span class="status-indicator" id="statusIndicator"></span>
                        <span class="status-text" id="statusText">Connecting...</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content" role="main">
            <!-- Chat Container -->
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages" role="log" aria-live="polite" aria-label="Chat conversation">
                    <div class="welcome-message">
                        <div class="message assistant-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot" aria-hidden="true"></i>
                            </div>
                            <div class="message-content">
                                <p>😒😒😒😒</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Uploaded Files (compact view) -->
                <div class="uploaded-files-compact" id="uploadedFiles" aria-label="Uploaded files" style="display: none;"></div>

                <!-- Input Area -->
                <div class="input-area">
                    <!-- Upload Progress (integrated) -->
                    <div class="upload-progress-integrated" id="uploadProgress" style="display: none;" role="progressbar" aria-valuemin="0" aria-valuemax="100">
                        <div class="progress-bar-integrated">
                            <div class="progress-fill-integrated" id="progressFill"></div>
                        </div>
                        <span class="progress-text-integrated" id="progressText">Uploading...</span>
                    </div>

                    <div class="input-container">
                        <div class="input-wrapper">
                            <button
                                id="attachButton"
                                class="attach-button"
                                type="button"
                                title="Attach files"
                                aria-label="Attach files">
                                <i class="fas fa-paperclip" aria-hidden="true"></i>
                            </button>
                            <textarea
                                id="messageInput"
                                class="message-input"
                                placeholder="Type your message here..."
                                rows="1"
                                aria-label="Message input"
                                onkeydown="handleInputKeydown(event)"></textarea>
                            <button
                                id="sendButton"
                                class="send-button"
                                type="button"
                                aria-label="Send message"
                                onclick="sendMessage()">
                                <i class="fas fa-paper-plane" aria-hidden="true"></i>
                            </button>
                        </div>

                        <!-- Hidden file input -->
                        <input type="file"
                               id="fileInput"
                               class="file-input-hidden"
                               multiple
                               accept=".jpg,.jpeg,.png,.gif,.webp,.bmp,.svg,.pdf,.docx,.doc,.xlsx,.xls,.pptx,.ppt,.txt,.md,.html,.htm,.css,.js,.json,.xml,.csv,.rtf,.log,.py,.java,.cpp,.c,.h,.php,.rb,.go,.rs,.sql,.sh,.yaml,.yml"
                               aria-describedby="uploadInfo">
                    </div>

                    <!-- File upload info tooltip -->
                    <div class="upload-info-tooltip" id="uploadInfo">
                        <small>Supports: Images, Documents, Text/Code files • Max 10MB each • Multiple files</small>
                    </div>
                </div>
            </div>
        </main>

        <!-- Notifications -->
        <div class="notifications" id="notifications" aria-live="assertive" aria-atomic="true"></div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;" role="status" aria-label="Loading">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin" aria-hidden="true"></i>
                <p>Processing...</p>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
