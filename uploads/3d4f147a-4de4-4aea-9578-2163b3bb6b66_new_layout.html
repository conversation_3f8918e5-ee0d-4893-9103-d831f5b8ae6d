<body onload="initForm();" onfocus="onFocusForm();">
	<html:form action="syohinGroupListUpdate" method="post">
		<table
				cellspacing="0"
				cellpadding="0"
				border="0"
				width="100%">
			<tr>
				<td>
					<jsp:include page="header.jsp">
						<jsp:param name="PARAM" value="商品グループ登録（一覧）（AUT90041）" />
					</jsp:include>
				</td>
			</tr>
			<tr>
				<td align="center">
					<!-- ↓検索条件 -->
					<fvo:span property='term_detail_area1'>
						<div class="term">
							<div class="bunrui-group">
								<fvo:span property='term_bunrui1_area'>
									<table border="0" cellspacing="1" cellpadding="0" class="term">
										<tr>
											<th><fvo:span property='term_bunrui1_required'>*</fvo:span> <fvo:span property='term_bunrui1_title'></fvo:span></th>
											<td><fvo:text onblur="bunruiOnblur(this, 1);" property='term_bunrui1_cd' />
												<fvo:text property='term_bunrui1_na' styleClass="readOnly" />
												<fvo:button value="選 択" onclick="popup_select();" property='term_bunrui1_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='term_bunrui1_clear' styleClass="clearButton" /></td>
										</tr>
									</table>
								</fvo:span>
								<fvo:span property='term_bunrui2_area'>
									<table border="0" cellspacing="1" cellpadding="0" class="term">
										<tr>
											<th><fvo:span property='term_bunrui2_required'>*</fvo:span> <fvo:span property='term_bunrui2_title'></fvo:span></th>
											<td><fvo:text style="ime-mode:disabled;" onblur="bunruiOnblur(this, 2);" property='term_bunrui2_cd' />
												<fvo:text property='term_bunrui2_na' styleClass="readOnly" />
												<fvo:button value="選 択" onclick="popup_select();" property='term_bunrui2_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='term_bunrui2_clear' styleClass="clearButton" /></td>
										</tr>
									</table>
								</fvo:span>
								<fvo:span property='term_bunrui3_area'>
									<table border="0" cellspacing="1" cellpadding="0" class="term">
										<tr>
											<th><fvo:span property='term_bunrui3_required'>*</fvo:span> <fvo:span property='term_bunrui3_title'></fvo:span></th>
											<td><fvo:text style="ime-mode:disabled;" onblur="bunruiOnblur(this, 3);" property='term_bunrui3_cd' />
												<fvo:text property='term_bunrui3_na' styleClass="readOnly" />
												<fvo:button value="選 択" onclick="popup_select();" property='term_bunrui3_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='term_bunrui3_clear' styleClass="clearButton" />
											</td>
										</tr>
									</table>
								</fvo:span>
								<fvo:span property='term_bunrui4_area'>
									<table border="0" cellspacing="1" cellpadding="0" class="term">
										<tr>
											<th><fvo:span property='term_bunrui4_required'>*</fvo:span> <fvo:span property='term_bunrui4_title'></fvo:span></th>
											<td><fvo:text style="ime-mode:disabled;" onblur="bunruiOnblur(this, 4);" property='term_bunrui4_cd' />
												<fvo:text property='term_bunrui4_na' styleClass="readOnly" />
												<fvo:button value="選 択" onclick="popup_select();" property='term_bunrui4_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='term_bunrui4_clear' styleClass="clearButton" />
											</td>
										</tr>
									</table>
								</fvo:span>
								<fvo:span property='term_bunrui5_area'>
									<table border="0" cellspacing="1" cellpadding="0" class="term">
										<tr>
											<th><fvo:span property='term_bunrui5_required'>*</fvo:span> <fvo:span property='term_bunrui5_title'></fvo:span></th>
											<td><fvo:text style="ime-mode:disabled;" onblur="c(this, 5);" property='term_bunrui5_cd' />
												<fvo:text property='term_bunrui5_na' styleClass="readOnly" />
												<fvo:button value="選 択" onclick="popup_select();" property='term_bunrui5_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='term_bunrui5_clear' styleClass="clearButton" /></td>
										</tr>
									</table>
								</fvo:span>
							</div>
							<div class="syohin-group">
								<table border="0" cellspacing="1" cellpadding="0" class="term">
									<tr>
										<th>商品</th>
										<td><fvo:textarea property='term_syohin_cd_list'></fvo:textarea>
											<fvo:button value="クリア" onclick="this_getElementById('term_syohin_cd_list').value=''" property='syohin_clear' styleClass="clearButton" style="vertical-align:bottom; margin-bottom:1px;" />
										</td>
									</tr>
								</table>
							</div>
						</div>
					</fvo:span> <!-- ↑検索条件 -->
					<div class="term">
						<table class="term_btn_area">
							<tbody>
							<tr>
								<td>
									<center>
										<!-- ★ボタンを配置 -->
										<fvo:submit
												value="&emsp;検&emsp;索&emsp;"
												onclick="return doAction(this);"
												property='search_button'
												styleClass="controlButton" />
										<fvo:button
												value="&emsp;戻&emsp;る&emsp;"
												onclick="javascript:window.close();"
												property='exit_button'
												styleClass="controlButton" />
									</center>
								</td>
							</tr>
							</tbody>
						</table>
					</div>
					<div id="messageBase" class="term">
						<table class="term_msg_area" cellpadding="0" cellspacing="0" border="0" align="center">
							<tr>
								<td align="center">
									<fvo:span property='message'/>
								</td>
							</tr>
						</table>
					</div>
					<div class="list1" align="center">
						<fvo:span property='result_area'>
							<table cellpadding="0" cellspacing="0">
								<tr>
									<td style="text-align: left;">
										<div class="list1_search" align="left">
											<table class="list1_search"  cellpadding="0" cellspacing="0">
												<tr>
													<th class="list1_sentaku" style="width: 40px;">選択</th>
													<th class="list1_detail_button" style="width: 52px;">詳細</th>
													<th class="list1_syohin_group_cd" style="width: 140px;">商品グループコード</th>
													<th class="list1_syohin_group_na" style="width: 200px;">商品グループ名</th>
													<th class="list1_syohin_qt" style="width: 60px;">商品数</th>
													<th class="list1_insert_user" style="width: 90px;">登録者</th>
													<th class="list_insert_ts" style="width: 190px;">登録日時</th>
													<th class="list1_update_user" style="width: 90px;">更新者</th>
													<th class="list1_update_ts" style="width: 190px;">更新日時</th>
												</tr>
											</table>
										</div>
										<div id="scrollResult" class="list1_search scroll">
											<table class="list1_search"  cellpadding="0" cellspacing="0">
												<logic:iterate
														name="SyohinGroupListUpdateForm"
														property='result_rows'
														id="result_rows"
														indexId="result_rowsindex"
														type="jp.co.vinculumjapan.mdware.ordercommon.struts.form.rows.SyohinGroupListUpdateFormResult_rowsBean">
													<tr>
														<td class="list1_sentaku" align="center" style="width: 40px"><fvo:checkbox property='<%="result_rows[" + result_rowsindex + "].select"%>' /></td>
														<td class="list1_detail_button" align="center" style="width: 52px"><fvo:submit style="width:38px;" value="詳細" onclick="return doAction(this);" property='<%="result_rows[" + result_rowsindex + "].detail"%>' /></td>
														<td class="list1_syohin_group_cd" style="width: 140px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].syohin_group_cd"%>'></fvo:span></td>
														<td class="list1_syohin_group_na" style="width: 200px; text-align: center;"><fvo:text style="width: 190px;" maxlength="20" property='<%="result_rows[" + result_rowsindex + "].syohin_group_na"%>' /></td>
														<td class="list1_syohin_qt" style="width: 60px" class="numeric"><fvo:span property='<%="result_rows[" + result_rowsindex + "].syohin_count_qt"%>'></fvo:span></td>
														<td class="list1_insert_user" style="width: 90px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].insert_user_id"%>'></fvo:span></td>
														<td class="list_insert_ts" style="width: 190px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].insert_ts"%>'></fvo:span></td>
														<td class="list1_update_user" style="width: 90px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].update_user_id"%>'></fvo:span></td>
														<td class="list1_update_ts" style="width: 190px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].update_ts"%>'></fvo:span></td>
													</tr>
												</logic:iterate>
											</table>
										</div>
									</td>
								</tr>
							</table>
							<br>
							<!------ フッター部 START ------>
							<div align="center" class="list1">
								<table cellpadding="0" cellspacing="0" border="0" class="list1_btn_area_update">
									<tr>
										<td align="center">
											<fvo:submit value="&emsp;登&emsp;録&emsp;" onclick="return doAction(this);" property='update_button' />
											<fvo:submit value="&emsp;削&emsp;除&emsp;" onclick="return doAction(this);" property='delete_button' />
										</td>
									</tr>
								</table>
							</div>
							<!------ フッター部 END ------>
						</fvo:span>
					</div>
				</td>
			</tr>
		</table>
		<fvo:checkbox style="display: none;" value="1" property='confirmed' />
		<fvo:text style="display: none;" property='pre_method_name' />
		<jsp:include page="footer.jsp" />
	</html:form>
	</body>