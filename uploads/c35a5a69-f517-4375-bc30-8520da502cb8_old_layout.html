	<body onload="initForm();" onfocus="onFocusForm();" >
	<jsp:include page="header.jsp">
		<jsp:param name="PARAM" value="商品グループ登録（一覧）（AUT90041）" />
	</jsp:include>
	<html:form action="syohinGroupListUpdate" method="post" style="width: 995px; margin: 0px auto;">
		<fvo:checkbox style="display: none;" value="1" property='confirmed' />
		<fvo:text style="display: none;" property='pre_method_name' />

		<!-- ↓検索条件 -->
		<div id="term">
			<div style="width: 100%; text-align: left;">
				<!-- 50%にするとブラウザをドラッグしてリサイズした時にレイアウトが崩れるので、49.99%にしています -->
				<table class="bunrui" style="width: 49.99%;"></table>

				<!-- 検索条件.変動部 開始 -->
				<fvo:span property='term_detail_area1' >
					<table border="0" cellspacing="0" align="left" style="width: 49.99%; ">
						<tr>
							<td style="padding:0;">
								<fvo:span property='bunrui1_area' >
									<table class="bunrui" style="width:100%;">
										<tr>
											<th><fvo:span property='bunrui1_required' >*</fvo:span>&nbsp;<fvo:span property='bunrui1_title' ></fvo:span></th>
											<td>
												<fvo:text style="ime-mode: disabled;height:14px;  padding:2px 1px;margin:1px;" onblur="bunruiOnblur(this, 1);"  property='bunrui1_cd' />
												<fvo:text size="20" property='bunrui1_na' style="width: 110px; padding:1px;" styleClass="readOnly" />
												<fvo:button value="選 択"  onclick="popup_select();" property='bunrui1_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='bunrui1_clear' styleClass="clearButton" />
											</td>
										</tr>
									</table>
								</fvo:span>
								<fvo:span property='bunrui2_area' >
									<table class="bunrui" style="width:100%;">
										<tr>
											<th><fvo:span property='bunrui2_required' >*</fvo:span>&nbsp;<fvo:span property='bunrui2_title' ></fvo:span></th>
											<td>
												<fvo:text style="ime-mode:disabled;" onblur="bunruiOnblur(this, 2);" property='bunrui2_cd' />
												<fvo:text size="20" property='bunrui2_na' styleClass="readOnly" />
												<fvo:button value="選 択"  onclick="popup_select();" property='bunrui2_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='bunrui2_clear' styleClass="clearButton" />
											</td>
										</tr>
									</table>
								</fvo:span>
								<fvo:span property='bunrui3_area' >
									<table class="bunrui" style="width:100%;">
										<tr>
											<th><fvo:span property='bunrui3_required' >*</fvo:span>&nbsp;<fvo:span property='bunrui3_title' ></fvo:span></th>
											<td>
												<fvo:text style="ime-mode:disabled;" onblur="bunruiOnblur(this, 3);" property='bunrui3_cd' />
												<fvo:text size="20" property='bunrui3_na' styleClass="readOnly" />
												<fvo:button value="選 択"  onclick="popup_select();" property='bunrui3_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='bunrui3_clear' styleClass="clearButton" />
											</td>
										</tr>
									</table>
								</fvo:span>
								<fvo:span property='bunrui4_area' >
									<table class="bunrui" style="width:100%;">
										<tr>
											<th><fvo:span property='bunrui4_required' >*</fvo:span>&nbsp;<fvo:span property='bunrui4_title' ></fvo:span></th>
											<td>
												<fvo:text style="ime-mode:disabled;" onblur="bunruiOnblur(this, 4);" property='bunrui4_cd' />
												<fvo:text size="20" property='bunrui4_na' styleClass="readOnly" />
												<fvo:button value="選 択"  onclick="popup_select();" property='bunrui4_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='bunrui4_clear' styleClass="clearButton" />
											</td>
										</tr>
									</table>
								</fvo:span>
								<fvo:span property='bunrui5_area' >
									<table class="bunrui" style="width:100%;">
										<tr>
											<th><fvo:span property='bunrui5_required' >*</fvo:span>&nbsp;<fvo:span property='bunrui5_title' ></fvo:span></th>
											<td>
												<fvo:text style="ime-mode:disabled;" onblur="bunruiOnblur(this, 5);" property='bunrui5_cd' />
												<fvo:text size="20" property='bunrui5_na' styleClass="readOnly" />
												<fvo:button value="選 択"  onclick="popup_select();" property='bunrui5_button' styleClass="popupButton" />
												<fvo:button value="クリア" onclick="popup_clear();" property='bunrui5_clear' styleClass="clearButton" />
											</td>
										</tr>
									</table>
								</fvo:span>
							</td>
						</tr>
					</table>
					<table class="bunrui" align="left" style="width: 49.99%; border-top-style: none; margin-left: -1px;">
						<tr>
							<th>商品</th>
							<td>
								<fvo:textarea property='syohin_cd_list' ></fvo:textarea>
								<fvo:button value="クリア"  style="vertical-align:bottom; margin-bottom:1px;"  onclick="this_getElementById('syohin_cd_list').value=''" property='syohin_clear' styleClass="clearButton"  />
							</td>
						</tr>
					</table>
				</fvo:span>
			</div>
		</div>
		<!-- ↑検索条件 -->
		<table width="100%">
			<tr>
				<td style="text-align:center;">
					<!-- ★ボタンを配置 -->
					<fvo:submit value="&emsp;検&emsp;索&emsp;" onclick="return doAction(this);" property='search_button' styleClass="controlButton" />
					<fvo:button value="&emsp;戻&emsp;る&emsp;" onclick="javascript:window.close();" property='exit_button' styleClass="controlButton" /></td>
			</tr>
		</table>
		<table width="100%">
			<tr>
				<td align="center">
					<fvo:span style="display: inline; " property='message' ></fvo:span>
				</td>
			</tr>
		</table>
		<div align="center">
			<fvo:span property='result_area' >
				<table>
					<tr><td style="text-align: left;">
						<table class="list" style="text-align: center;">
							<thead>
							<tr style="height:14px;">
								<th style="width:40px;">選択</th>
								<th style="width:52px;">詳細</th>
								<th style="width:140px;">商品グループコード</th>
								<th style="width:200px;">商品グループ名</th>
								<th style="width:60px;">商品数</th>
								<th style="width:90px;">登録者</th>
								<th style="width:120px;">登録日時</th>
								<th style="width:90px;">更新者</th>
								<th style="width:120px;">更新日時</th>
							</tr>
							</thead>
							<tbody></tbody>
						</table>
						<div id="scrollResult" class="scroll" style="height: 300px;" >
							<table class="list">
								<logic:iterate name="SyohinGroupListUpdateForm" property='result_rows' id="result_rows" indexId="result_rowsindex" type="jp.co.vinculumjapan.mdware.ordercommon.struts.form.rows.SyohinGroupListUpdateFormResult_rowsBean" >
									<tr style="height:22px;">
										<td align="center" style=" width: 40px"><fvo:checkbox property='<%="result_rows[" + result_rowsindex + "].select"%>' /></td>
										<td align="center" style=" width: 52px"><fvo:submit style="width:38px; height:21px;" value="詳細" onclick="return doAction(this);" property='<%="result_rows[" + result_rowsindex + "].detail"%>' /></td>
										<td style=" width: 140px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].syohin_group_cd"%>' ></fvo:span></td>
										<td style=" width: 200px"><fvo:text style="width: 160px;" maxlength="20" size="30" property='<%="result_rows[" + result_rowsindex + "].syohin_group_na"%>' /></td>
										<td style=" width: 60px" class="numeric"><fvo:span property='<%="result_rows[" + result_rowsindex + "].syohin_count_qt"%>' ></fvo:span></td>
										<td style=" width: 90px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].insert_user_id"%>' ></fvo:span></td>
										<td style=" width: 120px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].insert_ts"%>' ></fvo:span></td>
										<td style=" width: 90px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].update_user_id"%>' ></fvo:span></td>
										<td  style="width: 120px"><fvo:span property='<%="result_rows[" + result_rowsindex + "].update_ts"%>' ></fvo:span></td>
									</tr>
								</logic:iterate>
							</table>
						</div>
					</td>
					</tr>
				</table>
				<fvo:submit value="&emsp;登&emsp;録&emsp;" onclick="return doAction(this);" property='update_button' styleClass="controlButton" />
				<fvo:submit value="&emsp;削&emsp;除&emsp;" onclick="return doAction(this);" property='delete_button' styleClass="controlButton" />
			</fvo:span>
		</div>
		<jsp:include page="footer.jsp" />
	</html:form></body>