<body onload="initForm();" onfocus="onFocusForm();" >
<!-- ѓVѓXѓeѓЂ‹¤’КѓЃѓjѓ…Ѓ[ѓoЃ[ START -->
<jsp:include page="header.jsp">
	<jsp:param name="PARAM" value="ЊxЌђЏрЊЏ“o^ЃiAUT90064Ѓj" />
</jsp:include>
<html:form action="alarmConditionInput" method="post" style="width: 995px; margin: 0px auto;"><fvo:checkbox style="display: none;" value="1" property='isclose' />
	<!-- ѓVѓXѓeѓЂ‹¤’КѓЃѓjѓ…Ѓ[ѓoЃ[ END -->
	<!-- ЊџЌхЌЂ–Ъ•” START -->
	<table width="100%" class="term" style="height: 24px;">
		<col width="10%"/>
		<col width="40%"/>
		<col width="10%"/>
		<col width="40%"/>
		<tr>
			<th>*”­’Ќ•ы–@</th>
			<td>
				<fvo:select-one property='hatyu_kb' >
				</fvo:select-one>
				<fvo:text style="display:none;" property='gyosyu_kb' />
			</td>
			<th>*“o^‹ж•Є</th>
			<td>
				<fvo:select-one property='toroku_kb' >
				</fvo:select-one>
			</td>
		</tr>
	</table>
	<table width="100%" class="term" style="height: 26px;">
		<col width="10%"/>
		<col width="40%"/>
		<col width="10%"/>
		<col width="40%"/>
		<tr>
			<th><fvo:span property='bunrui1_title' >*</fvo:span></th>
			<td>
				<fvo:text size="2" maxlength="2" style="width: 26px; ime-mode:disabled;" onblur="bunruiOnblur(this, 1);" property='bunrui1_cd' />
				<fvo:text size="30" tabindex="-1" property='bunrui1_na' styleClass="readonly" style="width: 166px;"/>
				<fvo:button value="‘I ‘р"  onclick="popup_select();" property='bunrui1_button' styleClass="popupButton" />
				<fvo:button value="ѓNѓЉѓA" onclick="popup_clear();" property='bunrui1_clear' styleClass="clearButton" />
			</td>
			<th><fvo:span property='bunrui2_title' ></fvo:span></th>
			<td>
				<fvo:text size="4" maxlength="4" style="ime-mode:disabled;" onblur="bunruiOnblur(this, 2);" property='bunrui2_cd' />
				<fvo:text size="30" tabindex="-1" property='bunrui2_na' styleClass="readonly" style="width: 166px;"/>
				<fvo:button value="‘I ‘р"  onclick="popup_select();" property='bunrui2_button' styleClass="popupButton" />
				<fvo:button value="ѓNѓЉѓA" onclick="popup_clear();" property='bunrui2_clear' styleClass="clearButton" />
			</td>
		</tr>
	</table>
	<table width="100%" class="term" style="height: 26px;">
		<col width="10%"/>
		<col width="40%"/>
		<col width="10%"/>
		<col width="40%"/>
		<tr>
			<th><fvo:span property='bunrui3_title' ></fvo:span></th>
			<td>
				<fvo:text size="6" maxlength="6" style="ime-mode:disabled;" onblur="bunruiOnblur(this, 3);" property='bunrui3_cd' />
				<fvo:text size="30" tabindex="-1" property='bunrui3_na' styleClass="readonly" style="width: 166px;"/>
				<fvo:button value="‘I ‘р"  onclick="popup_select();" property='bunrui3_button' styleClass="popupButton" />
				<fvo:button value="ѓNѓЉѓA" onclick="popup_clear();" property='bunrui3_clear' styleClass="clearButton" />
			</td>
			<th>ЌЂ–Ъ</th>
			<td>
				<fvo:select-one property='komoku_kb' >
				</fvo:select-one>
			</td>
		</tr>
	</table>
	<!-- ЊџЌхЌЂ–Ъ•” END -->
	<br>
	<!-- ѓTѓuѓ~ѓbѓgѓ{ѓ^ѓ“ START -->
	<fvo:submit value="&emsp;Њџ&emsp;Ќх&emsp;" onclick="return doAction(this);" property='retrieve' styleClass="controlButton" />
	<fvo:submit value="&emsp;–Я&emsp;‚й&emsp;" onclick="return doAction(this);" property='exit' styleClass="controlButton" />
	<!-- ѓTѓuѓ~ѓbѓgѓ{ѓ^ѓ“ END -->
	<br>
	<!-- ѓЃѓbѓZЃ[ѓW•” start -->
	<table width="100%">
		<tr>
			<td align="center">
				<fvo:span property='message' ></fvo:span>
			</td>
		</tr>
	</table>
	<!-- ѓЃѓbѓZЃ[ѓW•” end   -->
	<!-- –ѕЌЧ•” START -->
	<fvo:span property='meisai_block' >
		<table align="center"">
		<tr>
			<td>
				<table class="list">
					<thead>
					<tr height="28">
						<th width="60" ><%=ResorceUtil.getInstance().getPropertie("TNP_BUNRUI2_TITLE")%></th>
						<th width="120"><%=ResorceUtil.getInstance().getPropertie("TNP_BUNRUI2_TITLE")%>–ј</th>
						<th width="60" ><%=ResorceUtil.getInstance().getPropertie("TNP_BUNRUI3_TITLE")%></th>
						<th width="120"><%=ResorceUtil.getInstance().getPropertie("TNP_BUNRUI3_TITLE")%>–ј</th>

						<th width="250"><fvo:span property='meisai_midashi' ></fvo:span></th>
					</tr>
					</thead>
				</table>
				<div id="result_scroll_area" class="scroll" style="height: 300px;">
					<table class="list">
						<logic:iterate name="AlarmConditionInputForm" property='resultrow' id="resultrow" indexId="resultrowindex" type="jp.co.vinculumjapan.mdware.ordercommon.struts.form.rows.AlarmConditionInputFormResultrowBean" >
							<tr height="28">
								<td width="60"  align="center"><fvo:span property='<%="resultrow[" + resultrowindex + "].row_bunruino1cd"%>' ></fvo:span></td>
								<td width="120" align="center"><fvo:span property='<%="resultrow[" + resultrowindex + "].row_bunruino1na"%>' ></fvo:span></td>
								<td width="60"  align="center"><fvo:span property='<%="resultrow[" + resultrowindex + "].row_bunruino2cd"%>' ></fvo:span></td>
								<td width="120" align="center"><fvo:span property='<%="resultrow[" + resultrowindex + "].row_bunruino2na"%>' ></fvo:span></td>
								<td width="250" align="center">
									<fvo:span property='<%="resultrow[" + resultrowindex + "].row_comment1"%>' ></fvo:span>
									<fvo:text size="5" maxlength="3" style="ime-mode:disabled;" onkeydown="keyDown();" property='<%="resultrow[" + resultrowindex + "].row_conditionno1"%>' />
									<fvo:select-one onkeydown="keyDown();" property='<%="resultrow[" + resultrowindex + "].row_conditionno2"%>' >
									</fvo:select-one>
									<fvo:text size="5" maxlength="1" style="ime-mode:disabled;" onkeydown="keyDown();" property='<%="resultrow[" + resultrowindex + "].row_conditionno3"%>' />
									<fvo:span property='<%="resultrow[" + resultrowindex + "].row_comment2"%>' ></fvo:span>
								</td>
							</tr>
						</logic:iterate>
					</table>
				</div>
			</td>
		</tr>
		</table>
		<!-- ѓyЃ[ѓWѓ“ѓO•” START -->
		<jsp:include page="paging.jsp"/>
		<!-- ѓyЃ[ѓWѓ“ѓO•” END -->
		<!-- ѓTѓuѓ~ѓbѓgѓ{ѓ^ѓ“ START -->
		<fvo:submit value="&emsp;“o&emsp;^&emsp;" onclick="return doAction(this);" property='update' styleClass="controlButton" />
		<!-- ѓTѓuѓ~ѓbѓgѓ{ѓ^ѓ“ END -->
	</fvo:span>
	<!-- –ѕЌЧ•” END -->
	<!-- ѓVѓXѓeѓЂ‹¤’Кѓtѓbѓ^Ѓ[ START -->
	<!-- ѓVѓXѓeѓЂ‹¤’Кѓtѓbѓ^Ѓ[ END -->

</html:form></body>