# DeepSeek Chat Web Application

A modern, responsive web application for real-time streaming chat with file upload capabilities, powered by the DeepSeek API.

## 🌟 Features

### Chat Features
- **Real-time streaming responses** from DeepSeek API using WebSockets
- **Interactive web interface** with modern, responsive design
- **Conversation history** maintained during session
- **Typing indicators** and real-time message streaming
- **Mobile-friendly** responsive design

### File Upload Features
- **Drag-and-drop file upload** with visual feedback
- **Multiple file format support**: JPG, PNG, GIF, PDF, DOCX, TXT, MD
- **File size validation** (10MB maximum)
- **Real-time upload progress** with progress bars
- **Thumbnail generation** for image files
- **File type validation** using python-magic
- **Error handling** with clear user feedback

### Accessibility Features
- **ARIA attributes** for screen reader compatibility
- **Keyboard navigation** support throughout the interface
- **High contrast mode** support
- **Focus management** and visual indicators
- **Semantic HTML** structure

### User Experience
- **Real-time notifications** for success/error states
- **Loading indicators** and progress feedback
- **Responsive design** for desktop and mobile
- **Modern UI** with smooth animations and transitions
- **Error recovery** options for failed operations

## 🚀 Quick Start

1. **Clone and setup:**
   ```bash
   git clone <repository-url>
   cd chat-ui
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure API key:**
   ```bash
   cp .env.example .env
   # Edit .env and add your DeepSeek API key:
   # DEEPSEEK_API_KEY=your_actual_api_key_here
   ```

3. **Run the application:**
   ```bash
   python app.py
   # Or use the convenience script:
   ./run.sh
   ```

4. **Open in browser:**
   Navigate to `http://localhost:5000`

## 📁 Project Structure

```
chat-ui/
├── app.py                 # Main Flask application
├── deepseek_chat.py      # Original CLI version
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables template
├── run.sh              # Convenience startup script
├── templates/
│   └── index.html      # Main HTML template
├── static/
│   ├── css/
│   │   └── style.css   # Application styles
│   └── js/
│       └── app.js      # Frontend JavaScript
└── uploads/            # File upload directory

## 💻 Usage Guide

### Chat Interface
1. **Sending Messages:**
   - Type your message in the input field at the bottom
   - Press `Enter` to send or click the send button
   - Use `Shift + Enter` for new lines
   - Press `Escape` to clear the input field

2. **Real-time Responses:**
   - Messages stream in real-time as the AI generates them
   - Connection status is shown in the header
   - Conversation history is maintained during your session

### File Upload
1. **Upload Methods:**
   - **Drag & Drop:** Drag files directly onto the upload area
   - **Click to Select:** Click the upload area to open file browser
   - **Keyboard:** Use `Tab` to focus upload area, then `Enter` or `Space`

2. **Supported File Types:**
   - **Images:** JPG, JPEG, PNG, GIF (with thumbnail preview)
   - **Documents:** PDF, DOCX
   - **Text Files:** TXT, MD
   - **Size Limit:** Maximum 10MB per file

3. **Upload Process:**
   - Real-time progress bar during upload
   - Success/error notifications
   - File validation and type checking
   - Automatic thumbnail generation for images

### Keyboard Shortcuts
- `Enter` - Send message
- `Shift + Enter` - New line in message
- `Escape` - Clear message input
- `Tab` - Navigate between interface elements
- `Space/Enter` - Activate upload area when focused

## 🔧 Configuration

### Environment Variables
Create a `.env` file with the following variables:

```bash
# Required: DeepSeek API Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Optional: Flask Configuration
SECRET_KEY=your_secret_key_for_sessions
FLASK_ENV=development  # or production
```

### Application Settings
You can modify these settings in `app.py`:

```python
# File upload settings
app.config['MAX_CONTENT_LENGTH'] = 10 * 1024 * 1024  # 10MB
app.config['UPLOAD_FOLDER'] = 'uploads'

# Supported file extensions
ALLOWED_EXTENSIONS = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'pdf': 'application/pdf',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'txt': 'text/plain',
    'md': 'text/markdown'
}
```

## 🛠️ API Configuration

The application uses the DeepSeek API with these default settings:

- **Model:** `deepseek-chat`
- **Temperature:** 0.7 (creativity level)
- **Max Tokens:** 1000 (response length limit)
- **Base URL:** `https://api.deepseek.com`
- **Streaming:** Enabled for real-time responses

## 🔒 Security Features

- **File Type Validation:** Uses python-magic for MIME type verification
- **File Size Limits:** Configurable maximum file size (default 10MB)
- **Secure Filename Handling:** Uses werkzeug's secure_filename
- **Input Sanitization:** HTML escaping for user inputs
- **Environment Variables:** Secure API key storage
- **CSRF Protection:** Flask's built-in session security

## 📱 Browser Compatibility

### Supported Browsers
- **Chrome/Chromium:** 80+
- **Firefox:** 75+
- **Safari:** 13+
- **Edge:** 80+

### Mobile Support
- **iOS Safari:** 13+
- **Chrome Mobile:** 80+
- **Firefox Mobile:** 75+

### Required Features
- WebSocket support
- File API for drag-and-drop
- ES6 JavaScript features
- CSS Grid and Flexbox

## 🚨 Troubleshooting

### Common Issues

1. **Connection Failed:**
   ```
   Error: DEEPSEEK_API_KEY environment variable is required
   ```
   **Solution:** Ensure your `.env` file contains a valid DeepSeek API key

2. **File Upload Fails:**
   ```
   Error: File type not supported
   ```
   **Solution:** Check that your file is in a supported format and under 10MB

3. **WebSocket Connection Issues:**
   ```
   Error: Connection lost. Trying to reconnect...
   ```
   **Solution:** Check your internet connection and refresh the page

4. **Python Magic Import Error:**
   ```
   ImportError: failed to find libmagic
   ```
   **Solution:** Install libmagic system dependency:
   ```bash
   # Ubuntu/Debian
   sudo apt-get install libmagic1

   # macOS
   brew install libmagic

   # Windows
   pip install python-magic-bin
   ```

### Debug Mode
Run the application in debug mode for detailed error messages:

```bash
export FLASK_ENV=development
python app.py
```

## 🧪 Testing

### Manual Testing Checklist

#### Chat Functionality
- [ ] Send text messages
- [ ] Receive streaming responses
- [ ] Connection status updates
- [ ] Error handling for API failures
- [ ] Conversation history maintenance

#### File Upload
- [ ] Drag and drop files
- [ ] Click to upload files
- [ ] File type validation
- [ ] File size validation
- [ ] Progress indicators
- [ ] Thumbnail generation
- [ ] Error notifications

#### Accessibility
- [ ] Keyboard navigation
- [ ] Screen reader compatibility
- [ ] Focus management
- [ ] ARIA labels
- [ ] High contrast support

#### Responsive Design
- [ ] Desktop layout (1200px+)
- [ ] Tablet layout (768px-1199px)
- [ ] Mobile layout (<768px)
- [ ] Touch interactions
- [ ] Orientation changes

### Browser Testing
Test the application across different browsers and devices to ensure compatibility.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section above
- Review the DeepSeek API documentation

## 🔄 Version History

- **v2.0.0** - Web application with file upload
- **v1.0.0** - Command-line interface
```
